#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试主播稿格式统一功能
"""

import json
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_zhubo_format_directly():
    """直接测试主播稿格式统一功能"""
    
    print("🧪 直接测试主播稿格式统一功能")
    print("=" * 60)
    
    try:
        from page.txt.zhubo import ZhuboProcessor
        
        # 创建模拟的主控制器
        class MockController:
            def __init__(self):
                self.ai_optimized_data = {}
                self.zhibo_cache_dir = "src/cache/zhibo"
                
            def _get_ai_cache_key(self):
                return "a3ee86ff033eb4c6556150453704f738"
                
        mock_controller = MockController()
        zhubo_processor = ZhuboProcessor(mock_controller)
        
        print("✅ 成功创建ZhuboProcessor")
        
        # 读取真实的AI优化数据
        ai_cache_file = "src/cache/ai_optimized/ai_a3ee86ff033eb4c6556150453704f738.json"
        if os.path.exists(ai_cache_file):
            with open(ai_cache_file, 'r', encoding='utf-8') as f:
                ai_data = json.load(f)
            print("✅ 成功读取AI优化数据")
        else:
            print("❌ AI优化数据文件不存在")
            return False
        
        # 测试生成主播稿数据
        print("\n📊 测试生成主播稿数据...")
        zhubo_data = zhubo_processor._generate_zhubo_data(ai_data)
        
        print(f"✅ 生成主播稿数据成功，包含 {len(zhubo_data)} 个项目")
        
        # 验证所有项目的统一格式
        print("\n🔍 验证所有项目的统一格式...")
        all_items = []
        promotion_items = []
        optimized_items = []
        other_items = []
        
        for i, item in enumerate(zhubo_data):
            if isinstance(item, dict) and "list" in item:
                project_list = item["list"]
                for timestamp, content in project_list.items():
                    # 检查是否是统一的对象格式
                    if isinstance(content, dict) and "txt" in content and "is_adv" in content:
                        item_info = {
                            "项目索引": i,
                            "时间戳": timestamp,
                            "内容": content
                        }
                        all_items.append(item_info)
                        
                        # 根据is_adv值分类
                        if content["is_adv"] == "1":
                            promotion_items.append(item_info)
                        elif content["is_adv"] == "0":
                            optimized_items.append(item_info)
                        
                        print(f"✅ 统一格式项目:")
                        print(f"   时间戳: {timestamp}")
                        print(f"   文本: {content['txt'][:50]}...")
                        print(f"   is_adv: {content['is_adv']}")
                        if "source" in content:
                            print(f"   来源: {content['source']}")
                        print()
                    else:
                        # 格式不统一的项目
                        other_items.append({
                            "项目索引": i,
                            "时间戳": timestamp,
                            "内容": content
                        })
                        print(f"❌ 格式不统一的项目:")
                        print(f"   时间戳: {timestamp}")
                        print(f"   内容类型: {type(content)}")
                        if isinstance(content, str):
                            print(f"   内容: {content[:50]}...")
                        else:
                            print(f"   内容: {content}")
                        print()
        
        print(f"📊 统计结果:")
        print(f"   总项目数: {len(all_items)}")
        print(f"   推广文案项目 (is_adv=1): {len(promotion_items)}")
        print(f"   优化内容项目 (is_adv=0): {len(optimized_items)}")
        print(f"   格式不统一项目: {len(other_items)}")
        
        # 保存测试结果
        test_result_file = "test_zhubo_format_result.json"
        with open(test_result_file, 'w', encoding='utf-8') as f:
            json.dump(zhubo_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 测试结果已保存到: {test_result_file}")
        
        # 验证格式统一性
        if len(other_items) == 0:
            print("\n🎉 所有项目格式统一！修改成功！")
            return True
        else:
            print(f"\n⚠️ 还有 {len(other_items)} 个项目格式不统一，需要进一步修改")
            return False
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_zhubo_format_directly()
    if success:
        print("\n🎉 测试完成！主播稿格式统一功能正常工作")
    else:
        print("\n❌ 测试失败！需要检查代码实现")
