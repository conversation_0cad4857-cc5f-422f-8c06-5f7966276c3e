# -*- coding: utf-8 -*-
"""
TTS哈希基类模块 - 提供统一的文本哈希生成功能和缓存路径管理
确保所有TTS相关类使用一致的缓存键生成逻辑和统一的缓存路径
"""
import os
import hashlib
import configparser
from typing import Optional


class TTSHashBase:
    """TTS哈希基类 - 提供统一的文本哈希生成功能和缓存路径管理"""
    
    def __init__(self):
        """初始化基类，设置缓存配置"""
        self._cache_config = None
        self._load_config()
    
    def _load_config(self):
        """从配置文件加载缓存相关配置"""
        if self._cache_config is not None:
            return self._cache_config
        
        try:
            config = configparser.ConfigParser()
            # 构建配置文件路径（从项目根目录开始）
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                'src', 'config', 'config.ini'
            )
            
            if not os.path.exists(config_path):
                raise FileNotFoundError(f"配置文件未找到: {config_path}")  
            config.read(config_path, encoding='utf-8')
            self._cache_config = config['TTS']   
            return self._cache_config
   
        except Exception as e:
            raise e
    
    def _get_cache_file_path(self, text_hash: str) -> str:
        """
        根据文本哈希生成缓存文件路径
        
        Args:
            text_hash: 文本哈希值
            file_format: 文件格式，如果为None则使用默认格式
            
        Returns:
            str: 完整的缓存文件路径
        """
        cache_dir = self._cache_config['tts_path']
        format = self._cache_config['format']
        # 生成文件名
        filename = f"tts_{text_hash}.{format}"
        return os.path.join(cache_dir, filename)
    # 根据文本直接返回路径
    def _generate_text_path(self, text: str)->str:
        if self._cache_config is None:
            self._load_config()
        hash_str = self._generate_text_hash(text)
        return self._get_cache_file_path(hash_str)
    
    def _generate_text_hash(self, text: str) -> str:
        """
        生成文本的哈希值作为缓存键，包含TTS参数确保缓存一致性
        
        Args:
            text: 要生成哈希的文本内容
            
        Returns:
            str: MD5哈希值（32位小写字符串）
        """
        model  = self._cache_config['model']
        voice  = self._cache_config['voice']
        format = self._cache_config['format']
        cache_key_content = f"{text}||{model}||{voice}||{format}"
        # 生成MD5哈希
        hash_obj = hashlib.md5(cache_key_content.encode('utf-8'))
        return hash_obj.hexdigest()