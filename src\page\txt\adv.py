#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推广文案处理器模块 - 专门处理推广文案的生成和管理
基于原始数据和AI优化内容生成时间间隔推广文案
"""
import os
import json
import hashlib
import threading
import asyncio
import time
from datetime import datetime
from tkinter import messagebox


class AdvDataProcessor:
    """推广文案处理器 - 生成和管理推广文案"""
    
    def __init__(self, main_controller):
        self.main_controller = main_controller
        self.ai_optimizer = main_controller.ai_optimizer
        self.voice_cache_dir = main_controller.voice_cache_dir
    
    def _estimate_text_duration(self, text):
        """预估文本的语音时长（秒）"""
        if not isinstance(text, str):
            text = str(text)
        
        # 简化算法：中文字符0.5秒/字，英文字符0.1秒/字，标点符号0.2秒/个
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_chars = len([c for c in text if c.isalpha() and not ('\u4e00' <= c <= '\u9fff')])
        punctuation = len([c for c in text if c in '，。！？；：、""''（）【】'])
        
        duration = chinese_chars * 0.5 + english_chars * 0.1 + punctuation * 0.2
        return max(1.0, duration)  # 最少1秒
    
    async def generate_adv_data(self, progress_callback=None):
        """生成推广文案数据的主要方法 - 新流程"""
        try:
            if progress_callback:
                progress_callback("正在加载数据...", 5, "检查原始数据")
            
            # 步骤1: 获取original_data->data数据
            cache_key = self.main_controller._get_ai_cache_key()
            
            if cache_key not in self.main_controller.cached_data:
                return {
                    "success": False,
                    "error": "没有找到原始数据，请先获取数据"
                }
            
            original_data = self.main_controller.cached_data[cache_key]["data"]
            
            if not isinstance(original_data, list):
                return {
                    "success": False,
                    "error": "原始数据格式错误"
                }
            
            if progress_callback:
                progress_callback("正在处理项目数据...", 15, f"分析 {len(original_data)} 个项目")
            
            # 新数据结构
            adv_org_data = []
            processed_count = 0
            total_projects = len(original_data)
            
            # 步骤2: 遍历每个项目
            for i, project in enumerate(original_data):
                if not isinstance(project, dict):
                    continue
                
                # 步骤2.1: 跳过"考试完成"的项目
                info = project.get("info", {})
                project_name = info.get("name", "")
                if project_name == "考试完成":
                    print(f"⏭️ 跳过项目: {project_name}")
                    continue
                
                # 步骤2.2: 初始化txt
                txt = ""
                
                # 获取项目的list数据
                project_list = project.get("list", {})
                if not isinstance(project_list, dict) or not project_list:
                    print(f"⚠️ 项目 {project_name} 没有list数据")
                    continue
                
                # 步骤3: 按时间戳排序
                sorted_timestamps = sorted(project_list.keys(), key=lambda x: float(x))
                
                if len(sorted_timestamps) == 0:
                    continue
                
                print(f"🔄 处理项目: {project_name}, 共 {len(sorted_timestamps)} 个时间戳")
                
                # 创建新项目的list结构
                new_project_list = {}
                
                # 步骤3.1: 遍历时间戳进行差值计算
                for j, timestamp_str in enumerate(sorted_timestamps):
                    current_timestamp = float(timestamp_str)
                    current_text = project_list[timestamp_str]
                    
                    # 计算与下一个时间戳的差值
                    if j < len(sorted_timestamps) - 1:
                        # 不是最后一个，计算与下一个的差值
                        next_timestamp = float(sorted_timestamps[j + 1])
                        time_diff = next_timestamp - current_timestamp
                        
                        if time_diff < 9:
                            # 差值小于9秒，累积文本
                            txt += current_text + "。"
                            print(f"   📝 累积文本: {timestamp_str} (差值{time_diff:.1f}s < 9s)")
                        else:
                            # 差值大于等于9秒，触发推广逻辑
                            txt += current_text + "。"
                            
                            # 插入累积的文本到新结构
                            new_project_list[timestamp_str] = txt.rstrip("。")  # 移除最后的句号
                            
                            # 步骤4: 计算推广文案时间戳
                            estimated_duration = self._estimate_text_duration(current_text)
                            adv_timestamp = current_timestamp + estimated_duration + 2.0
                            
                            # 插入推广文案占位符（新格式：包含len和txt）
                            new_project_list[str(adv_timestamp)] = {
                                "len": time_diff,  # 使用计算出的时间差
                                "txt": ""          # 推广文案内容（初始为空）
                            }
                            
                            print(f"   ✅ 触发推广: {timestamp_str} -> {adv_timestamp} (差值{time_diff:.1f}s ≥ 9s)")
                            print(f"   📄 累积文本长度: {len(txt)} 字符")
                            
                            # 重置txt
                            txt = ""
                    
                    else:
                        # 步骤3.2: 最后一个时间戳的特殊处理
                        # 与下一个项目的start_time比较
                        next_project_start_time = None
                        if i + 1 < len(original_data):
                            next_project = original_data[i + 1]
                            if isinstance(next_project, dict):
                                next_info = next_project.get("info", {})
                                next_project_start_time = float(next_info.get("start_time", 0))
                        
                        if next_project_start_time is not None:
                            time_diff = next_project_start_time - current_timestamp
                            
                            if time_diff < 9:
                                # 小于9秒，跳过
                                print(f"   ⏭️ 最后时间戳跳过: {timestamp_str} (与下项目差值{time_diff:.1f}s < 9s)")
                            else:
                                # 大于等于9秒，触发推广逻辑
                                txt += current_text + "。"
                                
                                # 插入累积的文本
                                new_project_list[timestamp_str] = txt.rstrip("。")
                                
                                # 计算推广文案时间戳
                                estimated_duration = self._estimate_text_duration(current_text)
                                adv_timestamp = current_timestamp + estimated_duration + 2.0
                                
                                # 插入推广文案占位符（新格式：包含len和txt）
                                new_project_list[str(adv_timestamp)] = {
                                    "len": time_diff,  # 使用计算出的时间差
                                    "txt": ""          # 推广文案内容（初始为空）
                                }
                                
                                print(f"   ✅ 最后时间戳触发推广: {timestamp_str} -> {adv_timestamp} (与下项目差值{time_diff:.1f}s ≥ 9s)")
                        else:
                            # 没有下一个项目，直接处理当前文本
                            txt += current_text + "。"
                            new_project_list[timestamp_str] = txt.rstrip("。")
                            print(f"   📝 最后项目最后时间戳: {timestamp_str}")
                
                # 步骤5: 如果新project_list不为空，添加到结果中
                if new_project_list:
                    adv_org_data.append({"list": new_project_list})
                    processed_count += 1
                
                # 更新进度
                if progress_callback:
                    progress = 15 + (i + 1) / total_projects * 70
                    progress_callback(f"处理项目 {i + 1}/{total_projects}", progress, 
                                    f"已处理: {project_name}")
            
            if progress_callback:
                progress_callback("正在保存数据...", 90, f"保存 {processed_count} 个项目的数据")
            
            # 步骤6: 保存数据到缓存
            if not hasattr(self.main_controller, 'ai_optimized_data'):
                self.main_controller.ai_optimized_data = {}
            
            if cache_key not in self.main_controller.ai_optimized_data:
                self.main_controller.ai_optimized_data[cache_key] = {}
            
            # 保存为adv_org键值
            self.main_controller.ai_optimized_data[cache_key]["adv_org"] = adv_org_data
            
            # 保存到文件
            try:
                ai_cache_dir = self.main_controller.ai_cache_dir
                os.makedirs(ai_cache_dir, exist_ok=True)
                
                ai_cache_filename = f"ai_{cache_key}.json"
                ai_cache_path = os.path.join(ai_cache_dir, ai_cache_filename)
                
                # 读取现有文件（如果存在）
                existing_data = {}
                if os.path.exists(ai_cache_path):
                    with open(ai_cache_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                
                # 只保存adv_org数据
                existing_data["adv_org"] = adv_org_data
                
                # 保存文件
                with open(ai_cache_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 数据已保存到: {ai_cache_path}")
                
            except Exception as save_error:
                print(f"⚠️ 保存文件失败: {save_error}")
            
            if progress_callback:
                progress_callback("生成完成", 100, f"成功处理 {processed_count} 个项目")
            
            return {
                "success": True,
                "adv_data": adv_org_data,  # 修改键名匹配txtpage.py的期望
                "statistics": {
                    "total_projects": total_projects,
                    "processed_projects": processed_count,
                    "generated_items": sum(len(item["list"]) for item in adv_org_data)
                }
            }
            
        except Exception as e:
            print(f"❌ 生成推广文案数据失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": f"生成推广文案数据异常: {str(e)}"
            }
    
    def generate_adv_data_async(self, completion_callback, progress_callback=None):
        """异步生成推广文案"""
        def worker():
            try:
                # 进度回调函数（同时输出到控制台和GUI）
                def internal_progress_callback(message, progress=None, detail=""):
                    print(f"推广文案生成: {message}")
                    if progress_callback:
                        progress_callback(message, progress, detail)
                
                # 执行生成
                async def run_generation():
                    return await self.generate_adv_data(internal_progress_callback)
                
                result = asyncio.run(run_generation())
                
                # 在主线程中调用完成回调
                self.main_controller.root.after(0, lambda: completion_callback(result))
                
            except Exception as e:
                error_msg = f"推广文案异步生成异常: {str(e)}"
                print(f"❌ {error_msg}")
                
                # 通知进度回调有错误
                if progress_callback:
                    progress_callback(f"生成失败: {str(e)}", 0, "")
                
                result = {"success": False, "error": error_msg}
                self.main_controller.root.after(0, lambda: completion_callback(result))
        
        # 启动工作线程
        thread = threading.Thread(target=worker, daemon=True)
        thread.start() 
        """测试实际的生成逻辑处理"""
        print("\n🔬 详细测试实际生成逻辑...")
        
        # 模拟测试数据
        test_data = [
            {
                "info": {"name": "项目1", "start_time": 0},
                "list": {
                    "10.0": "文本1",
                    "15.0": "文本2",  # 5秒差值 < 9秒
                    "25.0": "文本3"   # 10秒差值 >= 9秒
                }
            },
            {
                "info": {"name": "考试完成", "start_time": 30},
                "list": {"35.0": "完成文本"}
            },
            {
                "info": {"name": "项目3", "start_time": 40},
                "list": {"45.0": "文本4"}
            }
        ]
        
        # 模拟处理逻辑
        adv_org_data = []
        processed_count = 0
        
        for i, project in enumerate(test_data):
            if not isinstance(project, dict):
                continue
            
            # 跳过"考试完成"的项目
            info = project.get("info", {})
            project_name = info.get("name", "")
            if project_name == "考试完成":
                print(f"⏭️ 跳过项目: {project_name}")
                continue
            
            # 初始化txt
            txt = ""
            
            # 获取项目的list数据
            project_list = project.get("list", {})
            if not isinstance(project_list, dict) or not project_list:
                print(f"⚠️ 项目 {project_name} 没有list数据")
                continue
            
            # 按时间戳排序
            sorted_timestamps = sorted(project_list.keys(), key=lambda x: float(x))
            
            if len(sorted_timestamps) == 0:
                continue
            
            print(f"\n🔄 处理项目: {project_name}, 共 {len(sorted_timestamps)} 个时间戳")
            
            # 创建新项目的list结构
            new_project_list = {}
            
            # 遍历时间戳进行差值计算
            for j, timestamp_str in enumerate(sorted_timestamps):
                current_timestamp = float(timestamp_str)
                current_text = project_list[timestamp_str]
                
                # 计算与下一个时间戳的差值
                if j < len(sorted_timestamps) - 1:
                    # 不是最后一个，计算与下一个的差值
                    next_timestamp = float(sorted_timestamps[j + 1])
                    time_diff = next_timestamp - current_timestamp
                    
                    if time_diff < 9:
                        # 差值小于9秒，累积文本
                        txt += current_text + "。"
                        print(f"   📝 累积文本: {timestamp_str} (差值{time_diff:.1f}s < 9s) -> txt='{txt}'")
                    else:
                        # 差值大于等于9秒，触发推广逻辑
                        txt += current_text + "。"
                        
                        # 插入累积的文本到新结构
                        final_txt = txt.rstrip("。")
                        new_project_list[timestamp_str] = final_txt
                        
                        # 计算推广文案时间戳
                        estimated_duration = self._estimate_text_duration(current_text)
                        adv_timestamp = current_timestamp + estimated_duration + 2.0
                        
                        # 插入推广文案占位符（新格式：包含len和txt）
                        new_project_list[str(adv_timestamp)] = {
                            "len": time_diff,  # 使用计算出的时间差
                            "txt": ""          # 推广文案内容（初始为空）
                        }
                        
                        print(f"   ✅ 触发推广: {timestamp_str} -> {adv_timestamp} (差值{time_diff:.1f}s ≥ 9s)")
                        print(f"   📄 累积文本: '{final_txt}'")
                        print(f"   🕐 推广时间戳: {adv_timestamp}")
                        
                        # 重置txt
                        txt = ""
                
                else:
                    # 最后一个时间戳的特殊处理
                    # 与下一个项目的start_time比较
                    next_project_start_time = None
                    if i + 1 < len(test_data):
                        next_project = test_data[i + 1]
                        if isinstance(next_project, dict):
                            next_info = next_project.get("info", {})
                            next_project_start_time = float(next_info.get("start_time", 0))
                    
                    if next_project_start_time is not None:
                        time_diff = next_project_start_time - current_timestamp
                        
                        if time_diff < 9:
                            # 小于9秒，跳过
                            print(f"   ⏭️ 最后时间戳跳过: {timestamp_str} (与下项目差值{time_diff:.1f}s < 9s)")
                        else:
                            # 大于等于9秒，触发推广逻辑
                            txt += current_text + "。"
                            
                            # 插入累积的文本
                            final_txt = txt.rstrip("。")
                            new_project_list[timestamp_str] = final_txt
                            
                            # 计算推广文案时间戳
                            estimated_duration = self._estimate_text_duration(current_text)
                            adv_timestamp = current_timestamp + estimated_duration + 2.0
                            
                            # 插入推广文案占位符（新格式：包含len和txt）
                            new_project_list[str(adv_timestamp)] = {
                                "len": time_diff,  # 使用计算出的时间差
                                "txt": ""          # 推广文案内容（初始为空）
                            }
                            
                            print(f"   ✅ 最后时间戳触发推广: {timestamp_str} -> {adv_timestamp} (与下项目差值{time_diff:.1f}s ≥ 9s)")
                            print(f"   📄 累积文本: '{final_txt}'")
                    else:
                        # 没有下一个项目，直接处理当前文本
                        txt += current_text + "。"
                        final_txt = txt.rstrip("。")
                        new_project_list[timestamp_str] = final_txt
                        print(f"   📝 最后项目最后时间戳: {timestamp_str} -> '{final_txt}'")
            
            # 如果新project_list不为空，添加到结果中
            if new_project_list:
                adv_org_data.append({"list": new_project_list})
                processed_count += 1
                print(f"   📦 项目结果: {new_project_list}")
        
        print(f"\n📊 测试结果总结:")
        print(f"   处理项目数: {processed_count}")
        print(f"   生成数据项数: {sum(len(item['list']) for item in adv_org_data)}")
        print(f"   最终数据结构: {adv_org_data}")
        
        return {"success": True, "adv_org_data": adv_org_data, "processed_count": processed_count} 