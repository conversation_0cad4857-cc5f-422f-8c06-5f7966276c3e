#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缓存文件中的错误时间戳
将推广文案的 "0.1" 时间戳修复为 "0"，"1999.9" 修复为 "2000"
"""
import os
import json
from datetime import datetime

def fix_cached_timestamp():
    """修复缓存文件中的错误时间戳"""
    cache_file = "src/cache/ai_optimized/ai_a3ee86ff033eb4c6556150453704f738.json"
    
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return False
    
    print(f"🔧 开始修复缓存文件中的错误时间戳: {cache_file}")
    
    # 创建备份
    backup_file = cache_file + ".timestamp_backup"
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"✅ 创建备份文件: {backup_file}")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return False
    
    # 修复 generated_content 字段中的时间戳
    try:
        if "generated_content" in data and data["generated_content"]:
            generated_data = json.loads(data["generated_content"])
            print(f"📊 原始 generated_content 包含 {len(generated_data)} 个项目")
            
            fixed_count = 0
            
            for i, item in enumerate(generated_data):
                if isinstance(item, dict) and "list" in item:
                    project_list = item["list"]
                    new_project_list = {}
                    
                    for timestamp, content in project_list.items():
                        new_timestamp = timestamp
                        
                        # 修复错误的时间戳
                        if timestamp == "0.1":
                            new_timestamp = "0"
                            print(f"🔧 修复时间戳: {timestamp} -> {new_timestamp}")
                            fixed_count += 1
                        elif timestamp == "1999.9":
                            new_timestamp = "2000"
                            print(f"🔧 修复时间戳: {timestamp} -> {new_timestamp}")
                            fixed_count += 1
                        
                        new_project_list[new_timestamp] = content
                    
                    item["list"] = new_project_list
            
            # 更新 generated_content
            data["generated_content"] = json.dumps(generated_data, ensure_ascii=False)
            print(f"✅ 修复了 {fixed_count} 个错误时间戳")
        
        # 修复 adv_data 字段中的时间戳（如果存在）
        if "adv_data" in data and data["adv_data"]:
            adv_data = data["adv_data"]
            print(f"📊 检查 adv_data 包含 {len(adv_data)} 个项目")
            
            adv_fixed_count = 0
            
            for i, item in enumerate(adv_data):
                if isinstance(item, dict) and "list" in item:
                    project_list = item["list"]
                    new_project_list = {}
                    
                    for timestamp, content in project_list.items():
                        new_timestamp = timestamp
                        
                        # 修复错误的时间戳
                        if timestamp == "0.1":
                            new_timestamp = "0"
                            print(f"🔧 修复 adv_data 时间戳: {timestamp} -> {new_timestamp}")
                            adv_fixed_count += 1
                        elif timestamp == "1999.9":
                            new_timestamp = "2000"
                            print(f"🔧 修复 adv_data 时间戳: {timestamp} -> {new_timestamp}")
                            adv_fixed_count += 1
                        
                        new_project_list[new_timestamp] = content
                    
                    item["list"] = new_project_list
            
            if adv_fixed_count > 0:
                print(f"✅ 修复了 adv_data 中 {adv_fixed_count} 个错误时间戳")
        
        # 更新时间戳
        data["timestamp"] = datetime.now().isoformat()
        
        # 保存修复后的数据
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 缓存文件修复完成")
        
        # 验证修复结果
        print("\n🔍 验证修复结果...")
        with open(cache_file, 'r', encoding='utf-8') as f:
            verified_data = json.load(f)
        
        if "generated_content" in verified_data and verified_data["generated_content"]:
            verified_generated = json.loads(verified_data["generated_content"])
            
            # 检查是否还有错误时间戳
            error_timestamps = []
            for item in verified_generated:
                if isinstance(item, dict) and "list" in item:
                    for timestamp in item["list"].keys():
                        if timestamp in ["0.1", "1999.9"]:
                            error_timestamps.append(timestamp)
            
            if error_timestamps:
                print(f"❌ 仍然存在错误时间戳: {error_timestamps}")
                return False
            else:
                print("✅ 验证通过，没有发现错误时间戳")
                return True
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        # 恢复备份
        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 已恢复备份文件")
        except Exception as restore_error:
            print(f"❌ 恢复备份失败: {restore_error}")
        return False

def show_timestamp_analysis():
    """显示时间戳分析"""
    cache_file = "src/cache/ai_optimized/ai_a3ee86ff033eb4c6556150453704f738.json"
    
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return
    
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("📊 时间戳分析报告")
        print("=" * 50)
        
        if "generated_content" in data and data["generated_content"]:
            generated_data = json.loads(data["generated_content"])
            
            all_timestamps = []
            promotion_timestamps = []
            opening_timestamps = []
            ending_timestamps = []
            
            for item in generated_data:
                if isinstance(item, dict) and "list" in item:
                    for timestamp, content in item["list"].items():
                        all_timestamps.append(timestamp)
                        
                        if isinstance(content, dict):
                            if content.get("source") == "开场白":
                                opening_timestamps.append(timestamp)
                            elif content.get("source") == "退场白":
                                ending_timestamps.append(timestamp)
                            elif content.get("is_adv") == "1":
                                promotion_timestamps.append(timestamp)
            
            print(f"总时间戳数量: {len(all_timestamps)}")
            print(f"开场白时间戳: {opening_timestamps}")
            print(f"退场白时间戳: {ending_timestamps}")
            print(f"推广文案时间戳: {promotion_timestamps}")
            
            # 检查问题时间戳
            problem_timestamps = [ts for ts in all_timestamps if ts in ["0.1", "1999.9"]]
            if problem_timestamps:
                print(f"❌ 发现问题时间戳: {problem_timestamps}")
            else:
                print("✅ 没有发现问题时间戳")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    print("🔧 缓存文件时间戳修复工具")
    print("=" * 50)
    
    # 显示修复前的分析
    print("\n📊 修复前分析:")
    show_timestamp_analysis()
    
    # 执行修复
    print("\n🔧 开始修复:")
    success = fix_cached_timestamp()
    
    # 显示修复后的分析
    print("\n📊 修复后分析:")
    show_timestamp_analysis()
    
    if success:
        print("\n🎉 缓存文件时间戳修复成功！")
        print("现在用户在生成主播稿时将看到正确的时间戳。")
    else:
        print("\n❌ 缓存文件时间戳修复失败！")
