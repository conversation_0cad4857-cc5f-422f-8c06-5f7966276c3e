#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的异步测试
"""
import asyncio

async def test_async_function():
    """测试异步函数"""
    print("开始异步操作...")
    await asyncio.sleep(0.1)
    print("异步操作完成")
    return {"success": True, "message": "测试成功"}

def test_asyncio_run():
    """测试asyncio.run"""
    print("🔍 测试asyncio.run...")
    
    try:
        result = asyncio.run(test_async_function())
        print(f"✅ 结果: {result}")
        
        if isinstance(result, dict) and result.get("success"):
            print("✅ asyncio.run工作正常")
            return True
        else:
            print("❌ 结果格式不正确")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    print("🚀 开始简单异步测试")
    print("=" * 30)
    
    if test_asyncio_run():
        print("=" * 30)
        print("✅ 测试通过")
    else:
        print("=" * 30)
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
