#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
files 函数模块
"""
import os
import json
import hashlib

class files:

    def save_single_cache(self, url, cache_data):
        """保存单个URL的缓存数据到JSON文件"""
        try:
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            cache_dir = os.path.join(current_dir, "src", "cache")
            filename  = self._url_to_filename(url)
            file_path = os.path.join(cache_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
            print(f"✅ 保存缓存文件: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"❌ 保存缓存失败 {url}: {e}")

    def _url_to_filename(self, url):
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        return f"{url_hash}.json"
    