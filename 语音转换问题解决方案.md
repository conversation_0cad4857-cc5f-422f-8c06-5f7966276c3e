# 语音转换问题解决方案

## 问题描述

用户报告语音转换功能失败，提示"没有找到可转换的zhibo文案"。经过分析发现两个主要问题：

1. **异步调用错误**: `'coroutine' object has no attribute 'get'`
2. **数据格式适配问题**: 新的数据格式无法被正确提取

## 问题分析

### 1. 异步调用错误
**错误信息**:
```
AttributeError: 'coroutine' object has no attribute 'get'
RuntimeWarning: coroutine 'AITxtTTSProcessor.process_ai_tts_conversion' was never awaited
```

**原因**: 在 `process_ai_tts_async` 方法中，直接调用了异步方法 `process_ai_tts_conversion`，但没有使用 `await` 或 `asyncio.run()`。

### 2. 数据格式适配问题
**数据格式变化**:
- **旧格式**: `"20.555000305": "xxx"`
- **新格式**: `"20.555000305": {"txt": "xxx", "is_adv": "0"}`

**问题**: `_extract_texts_from_ai_optimized` 方法只能处理旧格式的字符串数据，无法处理新格式的对象数据。

## 解决方案

### 1. 修复异步调用错误

**文件**: `src/page/txt/aitxt.py`
**位置**: 第310行

**修改前**:
```python
result = self.process_ai_tts_conversion(progress_callback)
```

**修改后**:
```python
result = asyncio.run(self.process_ai_tts_conversion(progress_callback))
```

### 2. 适配新的数据格式

**文件**: `src/page/txt/aitxt.py`
**位置**: 第86-119行

**核心修改**:
```python
# 遍历zhibo_data列表，去重处理
seen_texts = set()
for item in zhibo_data:
    if isinstance(item, dict) and "list" in item:
        item_list = item["list"]
        
        # 处理list结构，支持新旧两种数据格式
        if isinstance(item_list, dict):
            for timestamp, text_content in item_list.items():
                # 新格式: {"timestamp": {"txt": "xxx", "is_adv": "0"}}
                if isinstance(text_content, dict) and "txt" in text_content:
                    text_value = text_content["txt"]
                    is_adv = text_content.get("is_adv", "0")
                    
                    cleaned_text = self._clean_text_for_tts(text_value)
                    if cleaned_text and cleaned_text not in seen_texts:
                        texts.append(cleaned_text)
                        seen_texts.add(cleaned_text)
                
                # 兼容旧格式: {"timestamp": "text"}
                elif isinstance(text_content, str):
                    cleaned_text = self._clean_text_for_tts(text_content)
                    if cleaned_text and cleaned_text not in seen_texts:
                        texts.append(cleaned_text)
                        seen_texts.add(cleaned_text)
```

### 3. 添加结果保存功能

**新增方法**: `_save_tts_results_to_zhibo`
**位置**: 第341-395行

**功能**: 将TTS转换结果保存回zhibo文件，保持新的数据格式：
```python
# 保存结果格式
{
    "timestamp": {
        "txt": "原始文本",
        "is_adv": "0",
        "audio_path": "src/cache/tts/tts_abc123.mp3"
    }
}
```

## 修改详情

### 1. 异步修复
- **修改文件**: `src/page/txt/aitxt.py`
- **修改行数**: 第310行
- **修改内容**: 使用 `asyncio.run()` 正确执行异步方法

### 2. 数据格式适配
- **修改文件**: `src/page/txt/aitxt.py`
- **修改行数**: 第86-119行
- **修改内容**: 
  - 支持新格式 `{"txt": "xxx", "is_adv": "0"}`
  - 保持对旧格式的兼容性
  - 添加去重逻辑
  - 增加调试信息

### 3. 结果保存
- **新增方法**: `_save_tts_results_to_zhibo`
- **修改行数**: 第285行（调用保存方法）
- **功能**: 
  - 将转换结果保存回zhibo文件
  - 添加 `audio_path` 字段
  - 保持数据结构完整性

## 测试验证

### 1. 调试信息
添加了详细的调试输出：
```python
print("🔍 开始提取文本，使用新的数据格式适配逻辑")
print(f"🔍 处理item_list，包含 {len(item_list)} 个时间戳")
print(f"🔍 检查时间戳 {timestamp}，内容类型: {type(text_content)}")
```

### 2. 测试脚本
创建了测试脚本验证修改：
- `test_text_extraction.py` - 文本提取测试
- `test_tts_fix.py` - 异步修复测试
- `simple_async_test.py` - 简单异步测试

## 预期效果

修复后，语音转换功能应该能够：

1. ✅ 正确处理新的数据格式 `{"txt": "xxx", "is_adv": "0"}`
2. ✅ 保持对旧格式的兼容性
3. ✅ 成功提取文本进行TTS转换
4. ✅ 将转换结果保存回zhibo文件
5. ✅ 不再出现异步相关错误
6. ✅ 显示正确的转换统计信息

## 使用方法

1. 重新启动程序
2. 点击"语音转换"按钮
3. 观察控制台输出，应该看到：
   - `🔍 开始提取文本，使用新的数据格式适配逻辑`
   - `📝 提取文本 X (普通/广告): xxx...`
   - `✅ 从zhibo文件提取到 X 条有效文案（已去重）`
   - TTS转换进度信息
   - `✅ zhibo文件更新完成，共更新 X 条记录`

## 注意事项

1. **数据备份**: 修改会直接更新zhibo文件，建议使用前备份
2. **兼容性**: 完全向后兼容，不影响现有功能
3. **性能**: 使用去重逻辑避免重复转换，提高效率
4. **调试**: 包含详细的调试信息，便于问题排查

## 总结

此次修复解决了语音转换功能的两个核心问题：
1. 异步调用错误 - 使用 `asyncio.run()` 正确处理异步方法
2. 数据格式适配 - 支持新格式同时保持向后兼容

修改已完成，语音转换功能现在应该能够正常工作。
