#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的VoiceProcessor文本提取功能
"""
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_voice_processor_fix():
    """测试修复后的VoiceProcessor"""
    print("🧪 测试修复后的VoiceProcessor文本提取")
    print("=" * 60)
    
    try:
        from src.page.index.voice import VoiceProcessor
        
        # 创建模拟的主控制器
        class MockMainController:
            def __init__(self):
                self.zhibo_cache_dir = "src/cache/zhibo"
                self.tts_plugin = None
                
            def _get_ai_cache_key(self):
                return "a3ee86ff033eb4c6556150453704f738"
        
        # 创建VoiceProcessor实例
        main_controller = MockMainController()
        voice_processor = VoiceProcessor(main_controller)
        
        print("✅ VoiceProcessor创建成功")
        
        # 测试generate_zhibo_voice_data方法
        print("\n🎵 测试generate_zhibo_voice_data方法:")
        voice_processor.generate_zhibo_voice_data()
        
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 VoiceProcessor修复验证测试")
    print("=" * 80)
    
    success = test_voice_processor_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试通过！文本提取问题已修复")
    else:
        print("⚠️ 测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
