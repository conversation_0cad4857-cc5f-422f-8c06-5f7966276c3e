# 端口管理工具模块说明

## 模块概述

端口管理工具模块 `src/utils/port_manager.py` 提供通用的端口管理和系统操作工具函数，支持跨平台的端口检查、清理和管理功能。

## 主要变更

### 1. 文件位置
- **`src/utils/port_manager.py`**: 端口管理工具模块（原 `src/plugin/utils.py`）

### 2. 修改的文件
- **`src/plugin/__init__.py`**: 添加工具函数的导出
- **`src/page/index.py`**: 移除端口处理代码，使用工具函数

## 工具函数模块功能

### 核心功能
1. **`check_and_kill_port_process(port=8765)`**: 检查并终止占用指定端口的进程
2. **`is_port_available(host, port)`**: 检查端口是否可用
3. **`find_available_port(host, start_port, max_attempts)`**: 寻找可用端口
4. **`get_system_info()`**: 获取系统信息
5. **`safe_kill_process(pid, force)`**: 安全地终止进程

### 跨平台支持
- **Windows**: 使用 `netstat` + `taskkill` 命令
- **Linux/macOS**: 使用 `lsof` + `kill` 命令

## 使用方式

### 在其他模块中使用
```python
from ..utils.port_manager import check_and_kill_port_process

# 检查并清理端口
check_and_kill_port_process(8765)
```

### 或者通过插件模块导入
```python
from ..plugin import check_and_kill_port_process
# 插件模块会自动从 port_manager 导入相关函数
```

## 重构优势

1. **代码复用**: 端口处理功能可以在多个模块中使用
2. **模块化**: 功能职责清晰分离
3. **维护性**: 集中管理相关工具函数
4. **可扩展**: 易于添加新的工具函数
5. **测试友好**: 独立模块便于单独测试

## 测试验证

工具模块包含测试代码，可以直接运行：
```bash
cd src/utils
python port_manager.py
```

输出示例：
```
🧪 测试端口工具函数...
系统信息: {'system': 'Windows', 'platform': 'Windows-10-10.0.22621-SP0', ...}
端口 8765 可用性: True
寻找可用端口: 8765
🔍 检查端口 8765 占用情况...
✅ 端口 8765 未被占用
```

## 向后兼容

此重构保持了原有功能的完整性，对外部调用接口无影响。WbServer 启动前的端口清理功能仍然正常工作。

---

*重构完成时间: 2024年*  
*版本: v1.0.0* 