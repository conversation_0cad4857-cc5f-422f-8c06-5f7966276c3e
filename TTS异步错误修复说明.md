# TTS异步错误修复说明

## 问题描述

在使用语音转换功能时出现以下错误：

```
AttributeError: 'coroutine' object has no attribute 'get'
RuntimeWarning: coroutine 'AITxtTTSProcessor.process_ai_tts_conversion' was never awaited
```

## 错误原因

在 `src/page/txt/aitxt.py` 文件的 `process_ai_tts_async` 方法中，第310行：

```python
# 错误的调用方式
result = self.process_ai_tts_conversion(progress_callback)
```

`process_ai_tts_conversion` 是一个异步方法（async），但在调用时没有使用 `await` 或 `asyncio.run()`，导致返回的是协程对象而不是实际结果。

## 修复方案

### 修改的文件
`src/page/txt/aitxt.py`

### 修改的位置
第310行，在 `process_ai_tts_async` 方法中

### 修改前
```python
# 执行TTS转换 - 不再传递URL参数
result = self.process_ai_tts_conversion(progress_callback)
```

### 修改后
```python
# 执行TTS转换 - 使用asyncio.run来正确处理异步方法
result = asyncio.run(self.process_ai_tts_conversion(progress_callback))
```

## 修复详情

### 完整的修改代码

```python
def process_ai_tts_async(self, completion_callback):
    """异步处理zhibo文案TTS转换"""
    def worker():
        try:
            # 进度回调函数
            def progress_callback(message):
                # 由于没有progress_label，直接在控制台输出进度
                print(f"TTS转换: {message}")

            # 执行TTS转换 - 使用asyncio.run来正确处理异步方法
            result = asyncio.run(self.process_ai_tts_conversion(progress_callback))

            # 在主线程中调用完成回调
            self.main_controller.root.after(0, lambda: completion_callback(result))

        except Exception as e:
            error_msg = f"TTS异步处理异常: {str(e)}"
            print(f"❌ {error_msg}")
            result = {"success": False, "error": error_msg}
            self.main_controller.root.after(0, lambda: completion_callback(result))

    # 启动工作线程
    thread = threading.Thread(target=worker, daemon=True)
    thread.start()
```

## 修复原理

1. **问题根源**: `process_ai_tts_conversion` 是异步方法，直接调用返回协程对象
2. **解决方案**: 使用 `asyncio.run()` 来正确执行异步方法并获取结果
3. **执行流程**:
   - 在工作线程中调用 `asyncio.run()`
   - `asyncio.run()` 创建新的事件循环并执行异步方法
   - 返回实际的结果字典而不是协程对象
   - 回调函数接收到正确的结果格式

## 验证修复

修复后，语音转换功能应该能够：

1. ✅ 正确执行异步TTS转换
2. ✅ 返回正确格式的结果字典
3. ✅ 显示转换统计信息
4. ✅ 不再出现协程相关错误

## 相关文件

- **主要修改**: `src/page/txt/aitxt.py` (第310行)
- **依赖检查**: `asyncio` 模块已正确导入 (第11行)

## 注意事项

1. **线程安全**: 修复在工作线程中使用 `asyncio.run()`，避免与主线程的事件循环冲突
2. **错误处理**: 保持原有的异常处理逻辑
3. **兼容性**: 修复不影响其他功能，只解决异步调用问题

## 测试建议

修复后建议测试以下场景：

1. 点击"语音转换"按钮
2. 观察控制台输出是否正常
3. 检查是否显示转换完成的统计信息
4. 验证zhibo文件是否正确更新音频路径

修复已完成，现在语音转换功能应该能够正常工作。
