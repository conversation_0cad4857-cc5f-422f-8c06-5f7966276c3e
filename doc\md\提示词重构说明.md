# 提示词重构说明

## 重构目标

解决 `_content_generate_async` 方法中 `prompt_text` 参数冗余的架构问题，统一使用插件的提示词管理机制。

## 重构前的问题

### 架构冲突
- **旧方式**：业务逻辑中硬编码提示词模板，格式化后传递给插件
- **新方式**：插件通过 `init_prompt()` 加载提示词文件到 `prompt_template` 属性
- **问题**：两种方式并存，造成冗余和混淆

### 具体问题代码
```python
# 问题1：硬编码提示词模板
"prompt_template": "根据文本生成亲切自然和强调解决痛点与避雷的可直接口播的开场白,\n课程名称中山茶亭科目三线路,返回包裹在json和中的JSON数据,文本为：{text}",

# 问题2：重复初始化和格式化
self.main_controller.ai_optimizer.init_prompt("prompty")  # 初始化插件提示词

### 2. 更新配置结构
```python
# 重构前
"prompt_template": "硬编码的提示词模板{text}",

# 重构后  
"prompt_file": "instr",  # 提示词文件名
```

### 3. 重构方法签名
```python
# 重构前
async def _content_generate_async(self, content_type, prompt_text):

# 重构后
async def _content_generate_async(self, content_type, original_text):
```

### 4. 优化调用流程
```python
# 重构后流程
self.main_controller.ai_optimizer.init_prompt(config["prompt_file"])
asyncio.run(self._content_generate_async(content_type, original_text))
```

## 重构效果

### 优势
1. **职责清晰**：业务逻辑处理数据流程，插件处理AI交互
2. **易于维护**：提示词集中在文件中管理，不散布在代码里  
3. **避免冲突**：消除双重提示词系统的混淆
4. **提高复用性**：插件更通用，提示词更易定制

### 兼容性
- 保持所有现有API调用接口不变
- 其他AI优化功能（如通用文案优化、推广文案优化）继续正常工作
- 插件内部提示词加载机制保持不变

## 验证结果

✅ DeepSeek插件成功加载开场白提示词文件
✅ DeepSeek插件成功加载退场白提示词文件  
✅ 豆包插件成功加载开场白提示词文件
✅ 开场白提示词文件存在且格式正确
✅ 退场白提示词文件存在且格式正确

## 影响范围

### 修改文件
- `src/page/txt/txtpage.py` - 重构核心方法和配置
- `src/prompty/instr.md` - 新增开场白提示词文件
- `src/prompty/end.md` - 新增退场白提示词文件

### 未修改文件
- 插件文件（DeepSeek、豆包）保持不变
- 其他AI优化流程保持不变
- 主控制器逻辑保持不变

## 总结

此次重构成功解决了 `_content_generate_async` 方法中 `prompt_text` 参数冗余的问题，实现了：

1. **统一架构**：全面采用插件提示词管理机制
2. **代码简化**：移除硬编码提示词模板
3. **维护性提升**：提示词文件化管理
4. **兼容性保证**：不影响现有功能

重构后的系统更加清晰、简洁、易维护，为后续功能扩展提供了良好的架构基础。 