import asyncio
import websockets
import json
import pygame
import os
import socket
from datetime import datetime
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WbServer:
    _instance = None
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(WbServer, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        
        # 初始化pygame音频模块
        pygame.mixer.init()
        
        # 当前播放状态
        self.current_time = 0
        self.is_playing = False
        self.connected_clients = set()
        self.server_port = None  # 记录实际使用的端口
        self.is_running = False  # 服务器运行状态
        self.server_task = None  # 服务器任务引用
        self.server = None  # WebSocket服务器实例
        
        # 添加启动确认事件
        self.server_started_event = None
        self.server_error = None
        
        # 当前直播URL
        self.current_url = None
        
        # 消息历史记录
        self.message_history: List[Dict[str, Any]] = []
        self.max_history_size = 100  # 最大历史记录数量
        
        # 监听插件
        self.listener_plugin = None
        
        # 全局事件循环管理
        self._loop = None
        self._loop_thread = None
    
    def add_to_history(self, message_type: str, direction: str, content: Dict[str, Any], client_info: str = ""):
        """添加消息到历史记录"""
        history_item = {
            "timestamp": datetime.now().isoformat(),
            "type": message_type,
            "direction": direction,  # "received" 或 "sent"
            "content": content,
            "client_info": client_info
        }
        self.message_history.append(history_item)
        
        # 保持历史记录在限制范围内
        if len(self.message_history) > self.max_history_size:
            self.message_history = self.message_history[-self.max_history_size:]
    
    def get_message_history(self) -> List[Dict[str, Any]]:
        """获取消息历史记录"""
        return self.message_history.copy()
    
    def clear_message_history(self):
        """清空消息历史记录"""
        self.message_history.clear()
    
    def init_listener_plugin(self):
        """初始化监听插件"""
        try:
            # 尝试多种导入方式
            try:
                # 导入src/anchors目录下的zhubo1模块
                import sys
                import os
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                anchors_path = os.path.join(project_root, 'src', 'anchors')
                sys.path.insert(0, anchors_path)
                from zhubo1 import ListenerPlugin
            except ImportError:
                # 备用导入方式
                import sys
                import os
                current_dir = os.path.dirname(__file__)
                project_root = os.path.dirname(os.path.dirname(current_dir))
                anchors_path = os.path.join(project_root, 'src', 'anchors')
                sys.path.insert(0, anchors_path)
                from zhubo1 import ListenerPlugin
            
            self.listener_plugin = ListenerPlugin(self)
            logger.info("✅ 监听插件初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 监听插件初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def set_current_url(self, url: str):
        """设置当前直播URL"""
        self.current_url = url
        logger.info(f"📝 设置当前直播URL: {url}")

    def start_listener(self, cache_key: str = None) -> bool:
        """开始监听"""
        if not self.listener_plugin:
            if not self.init_listener_plugin():
                return False
        
        # 传递cache_key给监听插件
        return self.listener_plugin.start_listening(cache_key)
    
    def stop_listener(self) -> bool:
        """停止监听"""
        if self.listener_plugin:
            return self.listener_plugin.stop_listening()
        return True
    
    def get_listener_status(self) -> Dict[str, Any]:
        """获取监听状态"""
        if self.listener_plugin:
            return self.listener_plugin.get_listener_status()
        return {"is_listening": False}
        
    def is_port_available(self, host, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception:
            return False
    
    def find_available_port(self, host="localhost", start_port=8765, max_attempts=10):
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            if self.is_port_available(host, port):
                return port
        return None

    async def handle_websocket(self, websocket, path):
        """处理WebSocket连接 - 优化版本"""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        
        logger.info(f"🔌 新连接建立: {client_id}")
        self.connected_clients.add(websocket)
        logger.info(f"📊 当前连接数: {len(self.connected_clients)}")
        
        try:
            # 发送欢迎消息
            welcome_message = {
                "type": "welcome",
                "message": "WbServer连接成功",
                "server_time": datetime.now().isoformat(),
                "client_count": len(self.connected_clients)
            }
            await websocket.send(json.dumps(welcome_message))
            logger.info(f"📤 已发送欢迎消息给 {client_id}")
            self.add_to_history("welcome", "sent", welcome_message, client_id)
            
            # 简化：先不发送状态消息，避免复杂性
            logger.info(f"⏳ 开始监听来自 {client_id} 的消息...")
            
            # 保持连接活跃，监听消息
            async for message in websocket:
                try:
                    logger.info(f"📥 收到来自 {client_id} 的消息: {message}")
                    await self.process_message(websocket, message, client_id)
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ 收到无效JSON消息 {client_id}: {message}")
                    # 继续处理，不断开连接
                except Exception as msg_error:
                    logger.error(f"❌ 处理消息异常 {client_id}: {msg_error}")
                    # 继续处理其他消息，不断开连接
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 客户端正常断开: {client_id}")
        except Exception as e:
            logger.error(f"❌ WebSocket处理异常: {client_id}, 类型: {type(e).__name__}, 详情: {e}")
        finally:
            # 清理连接
            if websocket in self.connected_clients:
                self.connected_clients.discard(websocket)
            logger.info(f"🔄 连接清理完成: {client_id}, 剩余连接数: {len(self.connected_clients)}")
    
    async def process_message(self, websocket, message, client_id):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            
            # 记录接收到的消息
            self.add_to_history(msg_type, "received", data, client_id)
            
            if msg_type == "time_update":
                # 更新当前播放时间
                self.current_time = data.get("currentTime", 0)
                logger.info(f"收到时间更新: {self.current_time:.2f}秒")
                
                # 如果监听插件已启用，处理时间更新
                if self.listener_plugin and self.listener_plugin.is_listening:
                    await self.listener_plugin.handle_time_update(self.current_time)
 
            elif msg_type == "end":
                # 更新播放状态
                 await self.listener_plugin.play_voice_by_key("2000")
               
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    async def broadcast_message(self, message):
        """向所有连接的客户端广播消息"""
        if self.connected_clients:
            message_json = json.dumps(message)
            # 记录广播消息
            self.add_to_history(message.get("type", "broadcast"), "sent", message, "all_clients")
            # 使用gather并发发送给所有客户端
            await asyncio.gather(
                *[client.send(message_json) for client in self.connected_clients],
                return_exceptions=True
            )
    
    async def send_control_message(self, command: str, data: Dict[str, Any] = None):
        """向客户端发送控制消息 (start, pause, goon, xm, help, switch)"""
        if command not in ["start", "pause", "goon", "xm", "help", "switch"]:
            logger.error(f"无效的控制命令: {command}")
            return False
        
        message = {
            "type": "control",
            "command": command,
            "timestamp": datetime.now().isoformat()
        }
        
        if data:
            message.update(data)
        
        try:
            await self.broadcast_message(message)
            logger.info(f"✅ 已发送控制消息: {command}")
            return True
        except Exception as e:
            logger.error(f"❌ 发送控制消息失败: {e}")
            return False
    
    async def send_start_command(self):
        """发送开始命令"""
        return await self.send_control_message("start")
    
    async def send_pause_command(self):
        """发送暂停命令"""
        return await self.send_control_message("pause")
    
    async def send_goon_command(self):
        """发送继续命令"""
        return await self.send_control_message("goon")
    
    async def send_xm_command(self, data: Dict[str, Any] = None):
        """发送xm命令"""
        return await self.send_control_message("xm", data)
    
    async def send_help_command(self):
        """发送帮助命令"""
        return await self.send_control_message("help")
    
    async def send_switch_command(self, data: Dict[str, Any] = None):
        """发送切换命令"""
        #三个 [1,2,3]
        return await self.send_control_message("switch", data)
    
    async def start_server(self, host="localhost", port=8765):
        """启动WebSocket服务器"""
        if self.is_running:
            logger.warning("⚠️ 服务器已在运行中")
            return True
        
        logger.info(f"🚀 正在启动WebSocket服务器...")
        
        # 检查默认端口是否可用
        if not self.is_port_available(host, port):
            logger.warning(f"⚠️ 端口 {port} 被占用，正在寻找可用端口...")
            available_port = self.find_available_port(host, port)
            if available_port:
                port = available_port
                logger.info(f"✅ 找到可用端口: {port}")
            else:
                logger.error(f"❌ 无法找到可用端口 (尝试范围: {port}-{port+9})")
                raise Exception("无法找到可用端口")
        
        self.server_port = port    
        try:
            # 创建启动确认事件
            self.server_started_event = asyncio.Event()
            self.server_error = None
            
            # 启动服务器任务
            self.server_task = asyncio.create_task(self._run_server(host, port))
            
            # 等待服务器启动确认（最多等待5秒）
            try:
                await asyncio.wait_for(self.server_started_event.wait(), timeout=5.0)
                
                # 检查是否有启动错误
                if self.server_error:
                    raise self.server_error
                
                # 验证端口确实在监听
                await asyncio.sleep(0.1)  # 给系统一点时间
                if self.is_port_available(host, port):
                    raise Exception(f"服务器启动后端口 {port} 仍然可用，可能启动失败")
                
                self.is_running = True
                logger.info(f"✅ WebSocket服务器启动成功，监听端口: {port}")
                return True
                
            except asyncio.TimeoutError:
                # 启动超时，取消任务
                if self.server_task:
                    self.server_task.cancel()
                raise Exception("WebSocket服务器启动超时")
                
        except OSError as e:
            if e.errno == 10048:  # Windows: 端口被占用
                logger.error(f"❌ 端口 {port} 被占用，请检查是否有其他程序在使用")
                logger.info(f"💡 解决方案: 关闭占用端口的程序，或使用不同端口")
            else:
                logger.error(f"❌ WebSocket服务器启动失败: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ WebSocket服务器启动异常: {e}")
            raise
    
    async def _run_server(self, host, port):
        """实际运行服务器的内部方法"""
        try:
            # 使用 websockets.serve 而不是 async with，这样服务器会持续运行
            self.server = await websockets.serve(
                self.handle_websocket, 
                host, 
                port,
                # 添加一些配置以提高兼容性
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            
            logger.info(f"📋 WebSocket服务器已绑定端口 {port}")
            logger.info(f"🔗 客户端连接地址: ws://{host}:{port}")
            
            # 发送启动成功信号
            if self.server_started_event:
                self.server_started_event.set()
            
            logger.info(f"📋 等待客户端连接...")
            # 等待服务器关闭
            await self.server.wait_closed()
                
        except Exception as e:
            logger.error(f"❌ WebSocket服务器内部错误: {e}")
            # 保存错误信息供start_server检查
            self.server_error = e
            # 发送启动信号（带错误）
            if self.server_started_event:
                self.server_started_event.set()
            raise
    
    async def stop_server(self):
        """停止WebSocket服务器"""
        if not self.is_running:
            logger.warning("⚠️ 服务器未在运行")
            return True
        
        try:
            self.is_running = False
            
            # 关闭所有客户端连接
            if self.connected_clients:
                await asyncio.gather(
                    *[client.close() for client in self.connected_clients],
                    return_exceptions=True
                )
                self.connected_clients.clear()
            
            # 关闭WebSocket服务器
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None
            
            # 取消服务器任务
            if self.server_task:
                self.server_task.cancel()
                try:
                    await self.server_task
                except asyncio.CancelledError:
                    pass
                self.server_task = None
            
            logger.info("✅ WebSocket服务器已停止")
            return True
        except Exception as e:
            logger.error(f"❌ 停止服务器时出错: {e}")
            return False
    
    def get_server_status(self):
        """获取服务器状态信息"""
        return {
            "is_running": self.is_running,
            "port": self.server_port,
            "client_count": len(self.connected_clients),
            "current_time": self.current_time,
            "is_playing": self.is_playing
        }

# 主程序
async def main():
    server = WbServer()
    await server.start_server()

if __name__ == "__main__":
    # 创建音频目录
    os.makedirs("audio", exist_ok=True)
    
    # 运行服务器
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器运行异常: {e}")
    finally:
        logger.info("👋 服务器已关闭") 