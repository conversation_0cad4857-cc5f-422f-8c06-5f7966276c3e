# 语音转换功能使用说明

## 功能概述

语音转换功能可以将AI优化后的文案批量转换为语音文件，并生成包含语音文件路径的JSON，支持缓存管理和播放试听。

## 核心功能

### 1. 批量语音转换
- 📊 **智能文本提取**：自动从AI优化结果中提取所有文本条目
- 🎵 **批量转换**：依次将每条文本转换为MP3语音文件
- 📦 **智能缓存**：相同文本自动使用缓存，避免重复转换
- ⚡ **进度跟踪**：实时显示转换进度和状态

### 2. 语音JSON生成
将原始的文本JSON转换为语音路径JSON：

**原始格式：**
```json
{
  "list": {
    "2.529000044": "起步环节注意：语音播报结束后，立即打开左转向灯",
    "4.531000137": "松开刹车踏板后，离合器要缓慢抬起",
    "20.555000305": "车身摆正后立即关闭转向灯"
  }
}
```

**转换后格式：**
```json
{
  "list": {
    "2.529000044": "./src/cache/voice/tts_xxx.mp3",
    "4.531000137": "./src/cache/voice/tts_xxx22.mp3", 
    "20.555000305": "./src/cache/voice/tts_xxx33.mp3"
  }
}
```

### 3. 缓存管理系统
- 🔍 **智能检查**：自动检查是否存在TTS结果缓存
- 💾 **自动保存**：转换结果自动保存到缓存目录
- 🗂️ **多级缓存**：
  - `src/cache/voice/` - 语音文件缓存
  - `src/cache/tts_results/` - TTS结果JSON缓存
- ✅ **完整性验证**：启动时验证语音文件完整性

### 4. 播放试听界面
- 🎵 **专用查看器**：独立的语音转换结果窗口
- ▶️ **一键播放**：每条语音都有播放试听按钮  
- 📊 **统计信息**：显示总计、成功、失败转换数量
- 🔄 **实时刷新**：支持刷新最新状态
- 💾 **导出功能**：支持保存语音JSON到指定位置

## 使用流程

### 第一步：准备AI优化数据
1. 在"文案"标签页输入URL地址
2. 点击"更新"获取原始文案数据
3. 点击"2AI文案优化"进行文案优化

### 第二步：执行语音转换
1. 点击"3语音转换"按钮
2. 系统自动检查缓存：
   - 如有缓存：直接显示结果查看器
   - 无缓存：开始批量转换

### 第三步：查看转换结果
转换完成后自动打开语音结果查看器，包含：
- 📈 **转换统计**：总计/成功/失败数量
- 📋 **语音列表**：按时间戳排序的语音条目
- ▶️ **播放按钮**：点击试听对应语音
- 📁 **文件信息**：显示文件名和大小

### 第四步：导出和使用
- 点击"💾 保存语音JSON"导出结果文件
- 点击"📁 打开语音文件夹"查看语音文件
- 使用生成的JSON进行后续开发

## 技术特性

### 语音合成配置
- **模型**：cosyvoice-v2（阿里云最新模型）
- **音色**：longanxuan（自然流畅的中文音色）  
- **格式**：MP3（兼容性最佳）
- **质量**：16kHz采样率，优质音质

### 文件命名规则
语音文件名格式：`tts_{文本哈希值}.mp3`
- 基于文本内容生成唯一哈希
- 相同文本生成相同文件名
- 自动去重和缓存复用

### 错误处理
- ❌ **转换失败**：在JSON中标记失败原因
- 🔄 **自动重试**：网络错误自动重试机制
- 📝 **详细日志**：完整的转换过程日志

## 文件结构

```
src/cache/
├── voice/                 # 语音文件缓存
│   ├── tts_hash1.mp3
│   ├── tts_hash2.mp3
├── tts_results/           # TTS结果缓存
│   ├── tts_result_key1.json
│   └── tts_result_key2.json
├── ai_optimized/          # AI优化结果
└── edited/                # 编辑缓存
```

## 常见问题

### Q: 语音转换失败怎么办？
A: 检查网络连接和API配置，系统会显示具体错误信息。

### Q: 如何修改语音音色？
A: 修改`src/plugin/tts_plugin.py`中的`voice`参数。

### Q: 支持批量播放吗？
A: 目前支持单条播放，批量播放功能可后续扩展。

## 技术架构

```mermaid
graph TD
    A[点击语音转换] --> B{检查TTS缓存}
    B -->|有缓存| C[显示结果查看器]
    B -->|无缓存| D[提取AI优化文本]
    D --> E[批量语音转换]
    E --> F[生成语音JSON]
    F --> G[保存TTS缓存]
    G --> C
    C --> H[播放试听]
    C --> I[导出JSON]
```

## 更新日志

### v1.0.0 (2024-12-28)
- ✅ 实现批量语音转换功能
- ✅ 支持智能缓存管理
- ✅ 添加播放试听界面
- ✅ 生成标准语音JSON格式
- ✅ 完整的错误处理和日志记录

---

*此功能基于阿里云CosyVoice-v2语音合成引擎，提供高质量的中文语音合成服务。* 