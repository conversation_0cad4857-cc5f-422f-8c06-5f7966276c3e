#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推广文案优化数据解析修复
"""

import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from page.txt.txtpage import TxtPage

def test_promotion_data_parsing():
    """测试推广文案数据解析修复"""
    
    # 模拟错误的豆包返回数据（您提到的问题）
    error_response = '''[
  {
    "list": {
      "4.531000137": "起步:语音播报后,打左灯(三秒后可转向), 看左后视镜,安全情况下起步 踩刹车,踩离合,挂一档,合手刹 观察显示面板 确认手刹完全放下。松刹车 注意缓慢放离合 不要导致车辆闯动 不要熄火",
      "17.531000137": {
        "len": 16.024000168,
        "txt": "[  \\n  {  \\n    \\"list\\": {  \\n      \\"4.531000137\\": \\"起步:语音播报后,打左灯(三秒后可转向), 看左后视镜,安全情况下起步 踩刹车,踩离合,挂一档,合手刹 观察显示面板 确认手刹完全放下。松刹车 注意缓慢放离合 不要导致车辆闯动 不要熄火\\",\\n      \\"17.531000137\\": {\\n        \\"len\\": 16.024000168,\\n        \\"txt\\": \\"正确的推广文案内容\\"\\n      }\\n    }\\n  }\\n]"
      }
    }
  }
]'''
    
    # 创建TxtPage实例（模拟）
    class MockMainController:
        def __init__(self):
            pass
    
    mock_controller = MockMainController()
    txt_page = TxtPage(mock_controller)
    
    print("=== 测试推广文案数据解析修复 ===")
    print(f"原始错误数据长度: {len(error_response)} 字符")
    
    try:
        # 测试解析content数据
        parsed_data = txt_page._parse_content_data(error_response)
        print(f"✅ 解析成功，类型: {type(parsed_data)}")
        
        if isinstance(parsed_data, list) and len(parsed_data) > 0:
            first_item = parsed_data[0]
            if isinstance(first_item, dict) and "list" in first_item:
                project_list = first_item["list"]
                print(f"📊 项目包含 {len(project_list)} 个时间戳")
                
                # 检查特定的时间戳
                if "17.531000137" in project_list:
                    content_17 = project_list["17.531000137"]
                    print(f"🔍 时间戳 17.531000137 的内容:")
                    print(f"   类型: {type(content_17)}")
                    
                    if isinstance(content_17, dict):
                        print(f"   字段: {list(content_17.keys())}")
                        if "txt" in content_17:
                            txt_content = content_17["txt"]
                            print(f"   txt长度: {len(str(txt_content))} 字符")
                            print(f"   txt开头: {str(txt_content)[:100]}...")
                            
                            # 检查是否还包含错误的JSON字符串
                            if isinstance(txt_content, str) and (
                                txt_content.strip().startswith('[') or 
                                txt_content.strip().startswith('{')
                            ):
                                print("❌ 仍然包含错误的JSON字符串格式")
                            else:
                                print("✅ txt内容格式正确")
        
        # 输出修复后的数据结构
        print("\n=== 修复后的数据结构 ===")
        print(json.dumps(parsed_data, indent=2, ensure_ascii=False)[:500] + "...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_promotion_data_parsing()
