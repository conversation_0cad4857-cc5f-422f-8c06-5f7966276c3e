# AI文案优化功能说明

## 🎯 功能概述

AI文案优化功能通过插件方式实现，使用DeepSeek API对获取的JSON数据进行智能优化，生成更清晰、易懂、实用的驾考文案内容。

## 🛠️ 技术实现

### 插件架构
- **配置文件**: `src/config/prompty.md`
- **集成方式**: 通过主程序动态加载插件

### API配置
- **基础URL**: `https://api.deepseek.com`
- **API密钥**: `***********************************`
- **模型**: `deepseek-reasoner`
- **温度参数**: `1.5`
- **最大Token**: `4000`

## 📁 文件结构

```
src/
├── plugin/
├── config/
│   └── prompty.md                 # 提示词模板
├── cache/
│   ├── [原始缓存文件].json        # 原始数据缓存
│   └── ai_optimized_*.json        # AI优化结果
└── integrated_launcher.py         # 主程序（已集成AI功能）
```

## 🚀 使用方法

### 1. 获取原始数据
1. 在"文案"标签页输入URL地址
2. 点击"更新"按钮获取JSON数据
3. 数据会自动缓存到 `src/cache/` 目录

### 2. 启动AI优化
1. 确保已有缓存数据
2. 点击"AI文案优化"按钮
3. 等待AI处理完成

### 3. 查看优化结果
- 优化完成后会弹出结果窗口
- 显示优化信息和处理后的文案
- 可复制结果到剪贴板
- 结果自动保存到缓存目录

## 🔧 插件功能特性

### 智能内容提取
```python
def _extract_content_from_json(self, json_data):
    """从JSON数据中提取需要优化的文案内容"""
    # 支持多种数据格式：
    # - 标准驾考数据格式（带msg和name字段）
    # - 原始内容格式（raw_content）
    # - 直接消息格式（message/content字段）
```

## 📋 优化处理流程

### 1. 数据预处理
- 从JSON中提取文案内容
- 识别项目名称和操作步骤
- 过滤无效或空白内容

### 2. 提示词构建
```
{李教练提示词模板}

## 需要优化的内容：
原始JSON数据摘要：
URL: {url}
时间: {timestamp}

提取的文案内容：
1. {content1}
2. {content2}
...

## 优化要求：
1. 针对每条文案进行优化，使其更加清晰易懂
2. 保持驾考专业术语的准确性
3. 优化语言表达，适合语音播报
4. 突出重点操作步骤和注意事项
5. 返回优化后的文案列表，格式清晰
```

### 3. API调用参数
```json
{
  "model": "deepseek-reasoner",
  "messages": [
    {
      "role": "user", 
      "content": "{构建的提示词}"
    }
  ],
  "temperature": 1.5,
  "max_tokens": 4000,
  "stream": false
}
```

### 4. 结果处理
- 解析API返回结果
- 构建标准化响应格式
- 保存到本地文件
- 显示在GUI界面

## 💡 优化效果示例

### 优化前
```
起步:语音播报后,打左灯(三秒后可转向), 看左后视镜,安全情况下起步
```

### 优化后（示例）
```
车辆起步操作：
1. 听到语音播报后，先打开左转向灯
2. 等待3秒后，通过左后视镜观察后方情况
3. 确认安全后，平稳起步行驶
注意：起步时要缓慢松离合，避免车辆闯动和熄火
```

## ⚙️ 配置说明

### API配置修改
```python
self.api_config = {
    "base_url": "https://api.deepseek.com",
    "api_key": "你的API密钥",
    "model": "deepseek-reasoner",
    "temperature": 1.5  # 调整创造性，0.1-2.0
}
```

### 提示词自定义
编辑 `src/config/prompty.md` 文件来定制优化风格：
- 调整角色设定
- 修改优化要求
- 添加特定约束条件

## 🔍 错误处理

### 常见错误及解决方案

1. **插件初始化失败**
   - 检查aiohttp是否安装：`pip install aiohttp>=3.8.0`
   - 确认插件文件路径正确

2. **API调用失败**
   - 验证API密钥有效性
   - 检查网络连接状态
   - 确认API配额充足

3. **数据格式错误**
   - 确保JSON数据格式正确
   - 检查是否有可提取的文案内容

4. **保存失败**
   - 确认写入权限
   - 检查磁盘空间

## 📊 性能监控

### 处理时间
- 数据提取：< 1秒
- API调用：5-30秒（取决于内容长度）
- 结果保存：< 1秒

### 资源消耗
- 内存使用：约10-50MB
- 网络流量：根据内容长度动态变化
- Token消耗：通常100-2000 tokens

## 🎯 使用建议

1. **批量处理**: 一次处理一个URL的数据，避免频繁调用
2. **内容检查**: 优化前先确认原始数据质量
3. **结果验证**: 人工检查AI优化结果的准确性
4. **提示词调优**: 根据实际效果调整提示词模板
5. **成本控制**: 合理使用，避免不必要的API调用

---

## 📞 技术支持

如遇问题，请检查：
1. 依赖包是否完整安装
2. API配置是否正确
3. 网络连接是否正常
4. 错误日志信息

**🎉 现在你可以享受AI驱动的智能文案优化了！** 