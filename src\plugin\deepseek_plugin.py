#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek插件
使用DeepSeek API对获取的JSON数据进行文案优化
"""

import os
import json
import requests
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import re
import aiohttp

class DeepSeekPlugin:
    def __init__(self, plugin_config=None):
        default_config = {
            "base_url": "https://api.deepseek.com",
        }
        # 如果提供了外部配置，则覆盖默认配置
        self.api_config = default_config.copy()
        if plugin_config:
            self.api_config.update(plugin_config)
       
    async def optimize_content_sync(self, json_data: Dict[str, Any], cache_key: str = None) -> Dict[str, Any]:
        try:        
            return self._call_deepseek_api_sync(json_data, cache_key)            
        except Exception as e:
            error_msg = f"❌ 同步优化失败: {e}"
            raise Exception(error_msg)

    def _build_api_request_data(self, content: str) -> tuple:
        headers = {
            "Authorization": f"Bearer {self.api_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.api_config["model"],
            "messages": [
                 {
                    "role": "system",
                    "content": self.prompt_template
                },
                {
                    "role": "user",
                    "content": content
                }
            ],
            "temperature": self.api_config["temperature"],
            "max_tokens": self.api_config.get("max_tokens"),  # 使用配置或默认值
            "stream": False
        }
        
        url = f"{self.api_config['base_url']}/chat/completions"
        
        return headers, payload, url
    
    def _validate_and_extract_response(self, response_data: dict) -> str:
        """返回数据处理"""
        # 检查响应结构
        if 'choices' not in response_data:
            raise Exception("API响应格式异常：缺少choices字段")
        
        if not response_data['choices']:
            raise Exception("API响应格式异常：choices为空")
        
        if 'message' not in response_data['choices'][0]:
            raise Exception("API响应格式异常：缺少message字段")
        
        content = response_data['choices'][0]['message']['content']
        
        return content

    def _save_raw_response(self, raw_response_text: str, cache_key: str = None) -> None:
        """保存原始API响应文本用于调试分析"""
        try:
            # 生成唯一标识
            if cache_key is None:
                cache_key = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存文件路径
            debug_dir = os.path.join("src", "cache", "ai_optimized", "debug")
            os.makedirs(debug_dir, exist_ok=True)
            
            debug_filename = f"deepseek_raw_response_{cache_key}_{timestamp}.txt"
            debug_path = os.path.join(debug_dir, debug_filename)
            
            # 保存原始响应文本
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(f"DeepSeek原始响应数据\n")
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"缓存键: {cache_key}\n")
                f.write(f"响应长度: {len(raw_response_text)} 字符\n")
                f.write("="*50 + "\n")
                f.write(raw_response_text)
            
            print(f"📝 DeepSeek原始响应已保存: {debug_filename}")
            
        except Exception as e:
            print(f"⚠️ 保存DeepSeek原始响应失败: {e}")

    def _call_deepseek_api_sync(self, content: str, cache_key: str = None) -> str:
        headers, payload, url = self._build_api_request_data(content)       
        try:
            response = requests.post( url, headers=headers, json=payload, timeout=300) 
            if response.status_code == 200:
                result = response.json()
                
                # 提取响应内容
                extracted_content = self._validate_and_extract_response(result)
                
                # 保存原始响应文本
                self._save_raw_response(extracted_content, cache_key)
                
                return extracted_content
            else:
                raise Exception(f"API调用失败 (状态码: {response.status_code}): {response.text}")
                
        except requests.RequestException as e:
            raise Exception(f"网络请求失败: {e}")
        except Exception as e:
            raise Exception(f"API调用异常: {e}")
    def init_prompt(self, prompt_file: str="empty"):
        """初始化提示词文件"""
        self.prompt_file = os.path.join("src/prompty", prompt_file+".md")
        self.prompt_template = self._load_prompt()
        
    def _load_prompt(self) -> str:
        if os.path.exists(self.prompt_file):
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                prompt_content = f.read()
            return prompt_content
        else:
            print(f"❌ 提示词文件不存在: {self.prompt_file}")
            return "请提供驾考文案优化的提示词。"
      