#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plugin Package
包含浏览器插件和相关功能
"""

from .browser_plugin import iPhone14LandscapeBrowserPlugin
from .deepseek_plugin import DeepSeekPlugin
from .doubao_plugin import DoubaoPlugin
from .tts_plugin import TTSPlugin
from .wb_server_plugin import WbServer
try:
    from ...utils.port_manager import (
        check_and_kill_port_process,
        is_port_available,
        find_available_port,
        get_system_info,
        safe_kill_process
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.utils.port_manager import (
        check_and_kill_port_process,
        is_port_available,
        find_available_port,
        get_system_info,
        safe_kill_process
    )

__all__ = [
    'iPhone14LandscapeBrowserPlugin',
    'DeepSeekPlugin',
    'DoubaoPlugin',
    'TTSPlugin',
    'WbServer',
    'check_and_kill_port_process',
    'is_port_available',
    'find_available_port',
    'get_system_info',
    'safe_kill_process'
]

__version__ = '1.0.0' 