# AI插件原始数据保存功能说明

## 功能概述

为了便于调试和分析AI插件的调用情况，在 `DoubaoPlugin` 和 `DeepSeekPlugin` 中增加了保存原始API响应数据的功能。当调用这两个插件时，除了正常处理业务逻辑外，还会自动保存服务器返回的原始文本响应到本地文件。

## 新增功能

### 1. 保存原始响应文本
- **文件位置**: `src/cache/ai_optimized/debug/`
- **文件命名**: `{plugin_name}_raw_response_{cache_key}_{timestamp}.txt`
  - `doubao_raw_response_a3ee86ff033eb4c6556150453704f738_20250115_143022.txt`
  - `deepseek_raw_response_a3ee86ff033eb4c6556150453704f738_20250115_143022.txt`

### 2. 保存的文件内容
每个保存的文本文件包含以下信息：
```
豆包原始响应数据
时间: 2025-01-15T14:30:22.123456
缓存键: a3ee86ff033eb4c6556150453704f738
响应长度: 1234 字符
==================================================
{服务器返回的原始文本内容}
```

## 设计特点

### 1. 简洁高效
- 仅保存服务器返回的核心文本内容，不包含复杂的JSON结构
- 文件体积小，便于快速查看和分析
- 参照推广文案优化功能的实现方式

### 2. 调试友好
- 文件头包含关键元信息：时间戳、缓存键、响应长度
- 采用纯文本格式，可以直接用记事本或任何文本编辑器打开
- 文件命名包含插件名称，便于区分不同AI服务的响应

## 代码修改说明

### 1. 插件方法签名更新
```python
# 原来的方法签名
async def optimize_content_sync(self, json_data: Dict[str, Any]) -> Dict[str, Any]:

# 新的方法签名
async def optimize_content_sync(self, json_data: Dict[str, Any], cache_key: str = None) -> Dict[str, Any]:
```

### 2. 简化的保存方法
在两个插件中都增加了简化的 `_save_raw_response` 方法：
```python
def _save_raw_response(self, raw_response_text: str, cache_key: str = None) -> None:
    """保存原始API响应文本用于调试分析"""
```

### 3. API调用流程更新
在 `_call_*_api_sync` 方法中，成功获取API响应后：
```python
if response.status_code == 200:
    result = response.json()
    
    # 提取响应内容
    extracted_content = self._validate_and_extract_response(result)
    
    # 保存原始响应文本 (新增)
    self._save_raw_response(extracted_content, cache_key)
    
    return extracted_content
```

### 4. 调用方更新
在 `txtpage.py` 中的三个调用位置都增加了缓存键参数传递：
```python
# 位置1: _content_generate_async
cache_key = self.main_controller._get_ai_cache_key()
optimized_ret = await self.main_controller.ai_optimizer.optimize_content_sync(prompt_text, cache_key)

# 位置2: _ai_optimize_async  
_result = await self.main_controller.ai_optimizer.optimize_content_sync(_data, cache_key)

# 位置3: _adv_optimize_async
cache_key = self.main_controller._get_ai_cache_key()
optimized_result = await self.main_controller.ai_optimizer.optimize_content_sync(optimization_request, cache_key)
```

## 使用场景

### 1. 调试AI响应
当AI优化结果不符合预期时，可以查看保存的原始响应文本，了解服务器实际返回的内容。

### 2. 内容分析
直接查看AI生成的原始文本，分析文案质量、格式结构等。

### 3. 问题排查
当出现内容解析异常时，可以通过原始响应文本分析问题原因。

### 4. 功能改进
通过分析多次调用的原始响应，优化提示词和处理逻辑。

## 注意事项

1. **磁盘空间**: 定期清理debug目录中的历史文件以节省空间
2. **敏感信息**: 保存的文件可能包含敏感内容，注意文件权限设置
3. **向后兼容**: 新增的`cache_key`参数是可选的，不传递时会自动生成时间戳作为唯一标识
4. **错误处理**: 保存原始数据的失败不会影响主业务逻辑，会打印警告日志

## 示例文件结构

```
src/cache/ai_optimized/debug/
├── doubao_raw_response_a3ee86ff033eb4c6556150453704f738_20250115_143022.txt
├── deepseek_raw_response_a3ee86ff033eb4c6556150453704f738_20250115_143025.txt
└── ai_raw_response_a3ee86ff033eb4c6556150453704f738_20250115_143030.txt
```

## 示例文件内容

```
DeepSeek原始响应数据
时间: 2025-01-15T14:30:22.123456
缓存键: a3ee86ff033eb4c6556150453704f738
响应长度: 892 字符
==================================================
```json
[
  {
    "name": "科目一模拟考试",
    "list": {
      "120.5": "欢迎来到科目一模拟考试，让我们一起学习交通安全知识",
      "245.8": "请仔细阅读每道题目，选择正确答案"
    }
  }
]
```

这里是AI优化后的内容说明：
1. 针对科目一考试场景进行了专门优化
2. 语言更加贴近学员需求
3. 增加了鼓励性的表述
```

## 实施完成

✅ DoubaoPlugin 原始文本保存功能已实现  
✅ DeepSeekPlugin 原始文本保存功能已实现  
✅ 调用方缓存键传递已更新  
✅ 向后兼容性已保证  
✅ 错误处理已完善  
✅ 参照推广文案优化实现，保持风格一致  

这个简化的实现方案更加实用，专注于保存最核心的原始响应文本，便于快速调试和问题分析。 