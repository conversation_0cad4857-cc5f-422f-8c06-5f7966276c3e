#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主播稿键值冲突修复功能
"""
import os
import sys
import json

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_key_conflict_fix():
    """测试键值冲突修复功能"""
    print("🧪 测试主播稿键值冲突修复功能")
    print("=" * 60)
    
    # 模拟包含冲突键值的AI优化数据
    mock_ai_data = {
        "optimized_content": [
            {
                "list": {
                    "10.5": "这是第一段优化内容",
                    "15.2": "这是第二段优化内容"
                }
            }
        ],
        "instr": "欢迎大家来到直播间，今天我们来学习科目三",
        "end": "感谢大家的观看，我们下次再见",
        "adv_data": [
            {
                "list": {
                    "0": {  # 这个键与开场白冲突
                        "txt": "今天开播就一个目的——把科三路考的通关干货给你们掰开揉碎讲透！",
                        "is_adv": "1"
                    },
                    "25.8": {
                        "txt": "限时优惠，现在报名立减200元！",
                        "is_adv": "1"
                    },
                    "2000": {  # 这个键与退场白冲突
                        "txt": "最后机会，错过今天就没有这个价格了！",
                        "is_adv": "1"
                    }
                }
            }
        ]
    }
    
    try:
        # 直接导入zhubo模块
        import importlib.util
        zhubo_spec = importlib.util.spec_from_file_location("zhubo", os.path.join(src_dir, "page", "txt", "zhubo.py"))
        zhubo_module = importlib.util.module_from_spec(zhubo_spec)
        zhubo_spec.loader.exec_module(zhubo_module)
        ZhuboDataProcessor = zhubo_module.ZhuboDataProcessor
        
        # 创建模拟的主控制器
        class MockController:
            def __init__(self):
                self.ai_optimized_data = {}
                self.root = None  # 添加root属性
                
        mock_controller = MockController()
        zhubo_processor = ZhuboDataProcessor(mock_controller)
        
        print("✅ 成功导入ZhuboDataProcessor")
        
        # 测试生成主播稿数据
        print("\n📊 测试生成主播稿数据（包含键值冲突）...")
        zhubo_data = zhubo_processor._generate_zhubo_data(mock_ai_data)
        
        print(f"✅ 生成主播稿数据成功，包含 {len(zhubo_data)} 个项目")
        
        # 验证键值冲突是否已修复
        print("\n🔍 验证键值冲突修复...")
        
        # 收集所有使用的键值
        all_keys = []
        key_usage = {}  # 记录每个键的使用情况
        
        for i, item in enumerate(zhubo_data):
            if isinstance(item, dict) and "list" in item:
                project_list = item["list"]
                for timestamp, content in project_list.items():
                    all_keys.append(timestamp)
                    
                    # 记录键的使用情况
                    if timestamp not in key_usage:
                        key_usage[timestamp] = []
                    
                    key_usage[timestamp].append({
                        "项目索引": i,
                        "内容": content,
                        "类型": "开场白" if timestamp == "0" and content.get("is_adv") == "0" 
                               else "退场白" if timestamp == "2000" and content.get("is_adv") == "0"
                               else "推广文案" if content.get("is_adv") == "1"
                               else "优化内容"
                    })
        
        # 检查是否有重复键值
        duplicate_keys = [key for key, usage in key_usage.items() if len(usage) > 1]
        
        if duplicate_keys:
            print(f"❌ 发现重复键值: {duplicate_keys}")
            for key in duplicate_keys:
                print(f"   键值 '{key}' 的使用情况:")
                for usage in key_usage[key]:
                    print(f"     - 项目{usage['项目索引']}: {usage['类型']}")
                    print(f"       内容: {usage['内容'].get('txt', str(usage['内容']))[:50]}...")
        else:
            print("✅ 没有发现重复键值，冲突修复成功！")
        
        # 验证特殊键值的正确使用
        print("\n🔍 验证特殊键值使用...")
        
        # 检查键值"0"是否只用于开场白
        if "0" in key_usage:
            zero_usage = key_usage["0"]
            if len(zero_usage) == 1 and zero_usage[0]["类型"] == "开场白":
                print("✅ 键值'0'正确用于开场白")
            else:
                print(f"❌ 键值'0'使用错误: {[u['类型'] for u in zero_usage]}")
        
        # 检查键值"2000"是否只用于退场白
        if "2000" in key_usage:
            end_usage = key_usage["2000"]
            if len(end_usage) == 1 and end_usage[0]["类型"] == "退场白":
                print("✅ 键值'2000'正确用于退场白")
            else:
                print(f"❌ 键值'2000'使用错误: {[u['类型'] for u in end_usage]}")
        
        # 检查推广文案是否使用了修复后的键值
        promotion_keys = [key for key, usage in key_usage.items() 
                         if any(u["类型"] == "推广文案" for u in usage)]
        
        print(f"\n📊 推广文案使用的键值: {promotion_keys}")
        
        # 输出完整的主播稿数据结构
        print("\n📄 完整的主播稿数据结构:")
        print(json.dumps(zhubo_data, indent=2, ensure_ascii=False))
        
        # 保存测试结果
        test_result_file = "test_key_conflict_fix_result.json"
        with open(test_result_file, 'w', encoding='utf-8') as f:
            json.dump(zhubo_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 测试结果已保存到: {test_result_file}")
        
        # 返回测试结果
        return len(duplicate_keys) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_key_conflict_fix()
    if success:
        print("\n🎉 键值冲突修复测试通过！")
    else:
        print("\n❌ 键值冲突修复测试失败！")
