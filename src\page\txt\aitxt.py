#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI文案TTS处理器模块 - 专门处理zhibo文案的语音转换
实现基于MD5的智能缓存检查，从指定的zhibo JSON文件读取文案进行TTS转换
"""
import os
import json
import hashlib
import threading
import asyncio
import time
from datetime import datetime
from tkinter import messagebox

from src.utils.tts_hash_base import TTSHashBase


class AITxtTTSProcessor(TTSHashBase):
    """AI文案TTS处理器 - 处理指定zhibo文件中的文案"""
    def __init__(self, main_controller):
        # 调用基类初始化方法
        super().__init__()
        self.main_controller = main_controller
        self.tts_plugin = main_controller.tts_plugin
    
    def _clean_text_for_tts(self, text):
        """清理文本，确保TTS API可以正确处理"""
        if not isinstance(text, str):
            text = str(text)
        
        # 去除前后空白
        text = text.strip()
        
        # 如果文本看起来像JSON，尝试解析获取实际内容
        if text.startswith('{') and text.endswith('}'):
            try:
                parsed = json.loads(text)
                # 如果是字典，尝试获取常见的文本字段
                if isinstance(parsed, dict):
                    for key in ['content', 'text', 'message', 'value']:
                        if key in parsed and isinstance(parsed[key], str):
                            text = parsed[key].strip()
                            break
            except:
                pass
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 检查文本是否有效（至少包含一个中文字符或英文字母）
        if not any(c.isalpha() or '\u4e00' <= c <= '\u9fff' for c in text):
            return None
        
        # 限制长度
        if len(text) > 2000:
            text = text[:2000]
        
        # 移除可能导致API错误的特殊字符
        text = text.replace('\x00', '').replace('\ufffd', '')
        
        return text if text else None
    
    def _extract_texts_from_ai_optimized(self):
        """直接从指定的zhibo缓存文件中提取文本 - 适配新的数据格式"""
        print("🔍 开始提取文本，使用新的数据格式适配逻辑")
        texts = []

        try:
            # 使用指定的zhibo缓存文件路径
            zhibo_cache_file = os.path.join(self.main_controller.zhibo_cache_dir, "a3ee86ff033eb4c6556150453704f738.json")

            if not os.path.exists(zhibo_cache_file):
                print(f"❌ 指定的zhibo缓存文件不存在: {zhibo_cache_file}")
                return texts

            # 读取zhibo缓存文件
            with open(zhibo_cache_file, 'r', encoding='utf-8') as f:
                zhibo_data = json.load(f)

            # 检查数据格式 - 应该是一个列表
            if not isinstance(zhibo_data, list):
                print("⚠️ zhibo文件格式不正确，应该是一个列表")
                return texts

            print(f"✅ 从zhibo文件加载数据: {len(zhibo_data)} 个项目组")

            # 遍历zhibo_data列表，去重处理
            seen_texts = set()
            for item in zhibo_data:
                if isinstance(item, dict) and "list" in item:
                    item_list = item["list"]

                    # 处理list结构，支持新旧两种数据格式
                    if isinstance(item_list, dict):
                        print(f"🔍 处理item_list，包含 {len(item_list)} 个时间戳")
                        for timestamp, text_content in item_list.items():
                            print(f"🔍 检查时间戳 {timestamp}，内容类型: {type(text_content)}")
                            # 新格式: {"timestamp": {"txt": "xxx", "is_adv": "0"}}
                            if isinstance(text_content, dict) and "txt" in text_content:
                                text_value = text_content["txt"]
                                is_adv = text_content.get("is_adv", "0")

                                cleaned_text = self._clean_text_for_tts(text_value)
                                if cleaned_text and cleaned_text not in seen_texts:
                                    texts.append(cleaned_text)
                                    seen_texts.add(cleaned_text)
                                    # 调试信息：显示前几个文本的内容和广告标识
                                    if len(texts) <= 3:
                                        adv_status = "广告" if is_adv == "1" else "普通"
                                        print(f"📝 提取文本 {len(texts)} ({adv_status}): {cleaned_text[:50]}...")

                            # 兼容旧格式: {"timestamp": "text"}
                            elif isinstance(text_content, str):
                                cleaned_text = self._clean_text_for_tts(text_content)
                                if cleaned_text and cleaned_text not in seen_texts:
                                    texts.append(cleaned_text)
                                    seen_texts.add(cleaned_text)
                                    # 调试信息：显示前几个文本的内容
                                    if len(texts) <= 3:
                                        print(f"📝 提取文本 {len(texts)} (旧格式): {cleaned_text[:50]}...")

            print(f"✅ 从zhibo文件提取到 {len(texts)} 条有效文案（已去重）")
            
        except Exception as e:
            print(f"❌ 从zhibo文件提取文案失败: {e}")
            import traceback
            traceback.print_exc()
        
        return texts
    
    def _check_text_cache_status(self, text):
        """检查单个文本的缓存状态 - 直接通过文件系统查找"""
        text_hash = self._generate_text_hash(text)
        audio_path = self._get_cache_file_path(text_hash)
        
        # 检查MP3文件是否存在
        if os.path.exists(audio_path):
            return {
                "hash": text_hash,
                "cached": True,
                "audio_path": audio_path
            }
        
        return {
            "hash": text_hash,
            "cached": False,
            "audio_path": audio_path
        }
    
    def _analyze_batch_cache_status(self, texts):
        """批量分析文本缓存状态"""
        analysis = {
            "total_count": len(texts),
            "cached_count": 0,
            "need_convert_count": 0,
            "cached_texts": [],
            "need_convert_texts": []
        }
        
        for text in texts:
            status = self._check_text_cache_status(text)
            
            if status["cached"]:
                analysis["cached_count"] += 1
                analysis["cached_texts"].append({
                    "text": text,
                    "status": status
                })
            else:
                analysis["need_convert_count"] += 1
                analysis["need_convert_texts"].append({
                    "text": text,
                    "status": status
                })
        
        return analysis
    
    async def process_ai_tts_conversion(self, progress_callback=None):
        """处理AI TTS转换的核心逻辑 - 直接从指定的zhibo文件读取"""
        try:
            texts = self._extract_texts_from_ai_optimized()
            if not texts:
                return {
                    "success": False,
                    "error": "没有找到可转换的zhibo文案"
                }
            
            print(f"📝 提取到 {len(texts)} 条zhibo文案")
            
            # 2. 分析缓存状态
            if progress_callback:
                progress_callback("正在分析缓存状态...")
                
            analysis = self._analyze_batch_cache_status(texts)
            print(f"📊 缓存分析: 总数={analysis['total_count']}, 缓存命中={analysis['cached_count']}, 需转换={analysis['need_convert_count']}")
            
            # 3. 检查TTS插件
            if not self.tts_plugin:
                return {
                    "success": False,
                    "error": "TTS插件未初始化"
                }
            
            # 4. 进行TTS转换
            tts_results = []
            success_count = 0
            error_count = 0
            
            # 添加缓存命中的结果
            for cached_item in analysis["cached_texts"]:
                tts_results.append({
                    "text": cached_item["text"],
                    "success": True,
                    "audio_path": cached_item["status"]["audio_path"],
                    "cached": True,
                    "hash": cached_item["status"]["hash"]
                })
                success_count += 1
            
            # 转换需要生成的文案
            for i, convert_item in enumerate(analysis["need_convert_texts"]):
                text = convert_item["text"]
                text_hash = convert_item["status"]["hash"]
                target_audio_path = convert_item["status"]["audio_path"]
                
                if progress_callback:
                    progress_callback(f"正在转换文案 {i+1}/{analysis['need_convert_count']}: {text[:30]}...")
                
                # 再次验证文本有效性
                cleaned_text = self._clean_text_for_tts(text)
                if not cleaned_text:
                    print(f"⚠️ 跳过无效文本: {text[:50]}...")
                    tts_results.append({
                        "text": text,
                        "success": False,
                        "error": "文本无效，无法转换",
                        "cached": False,
                        "hash": text_hash
                    })
                    error_count += 1
                    continue
                
                try:
                    # 使用清理后的文本进行转换
                    print(f"🎵 正在转换: {cleaned_text[:100]}...")
                    result = await self.tts_plugin.synthesize_async(cleaned_text)
                    
                    if result.get("success") and result.get("output_file"):
                        # 转换成功，将文件重命名为标准格式
                        original_file = result["output_file"]
                        
                        # 如果生成的文件不是目标路径，则移动文件
                        if original_file != target_audio_path:
                            try:
                                import shutil
                                shutil.move(original_file, target_audio_path)
                                print(f"📁 文件已移动到: {target_audio_path}")
                            except Exception as move_error:
                                print(f"⚠️ 文件移动失败: {move_error}，使用原路径")
                                target_audio_path = original_file
                        
                        tts_results.append({
                            "text": cleaned_text,
                            "success": True,
                            "audio_path": target_audio_path,
                            "cached": False,
                            "hash": text_hash
                        })
                        success_count += 1
                        
                    else:
                        # 转换失败
                        error_msg = result.get("error", "TTS转换失败")
                        print(f"❌ TTS转换失败: {cleaned_text[:50]}... - {error_msg}")
                        
                        tts_results.append({
                            "text": text,
                            "success": False,
                            "error": error_msg,
                            "cached": False,
                            "hash": text_hash
                        })
                        error_count += 1
                        
                except Exception as e:
                    print(f"❌ TTS转换异常: {cleaned_text[:50]}... - {e}")
                    tts_results.append({
                        "text": text,
                        "success": False,
                        "error": str(e),
                        "cached": False,
                        "hash": text_hash
                    })
                    error_count += 1
                
                # 短暂延迟，避免请求过于频繁
                time.sleep(0.1)
            
            # 5. 返回结果
            return {
                "success": True,
                "statistics": {
                    "total_texts": len(texts),
                    "success_count": success_count,
                    "error_count": error_count,
                    "cache_hit_count": analysis["cached_count"]
                },
                "results": tts_results
            }
            
        except Exception as e:
            print(f"❌ AI TTS转换处理异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": f"TTS处理异常: {str(e)}"
            }
    
    def process_ai_tts_async(self, completion_callback):
        """异步处理zhibo文案TTS转换"""
        def worker():
            try:
                # 进度回调函数
                def progress_callback(message):
                    # 由于没有progress_label，直接在控制台输出进度
                    print(f"TTS转换: {message}")

                # 执行TTS转换 - 使用asyncio.run来正确处理异步方法
                result = asyncio.run(self.process_ai_tts_conversion(progress_callback))

                # 在主线程中调用完成回调
                self.main_controller.root.after(0, lambda: completion_callback(result))

            except Exception as e:
                error_msg = f"TTS异步处理异常: {str(e)}"
                print(f"❌ {error_msg}")
                result = {"success": False, "error": error_msg}
                self.main_controller.root.after(0, lambda: completion_callback(result))

        # 启动工作线程
        thread = threading.Thread(target=worker, daemon=True)
        thread.start()
    