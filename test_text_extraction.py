#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本提取功能
"""
import os
import json

def test_text_extraction():
    """测试文本提取"""
    print("🔍 测试文本提取功能...")
    
    # 模拟zhibo文件路径
    zhibo_file = "src/cache/zhibo/a3ee86ff033eb4c6556150453704f738.json"
    
    if not os.path.exists(zhibo_file):
        print(f"❌ zhibo文件不存在: {zhibo_file}")
        return False
    
    try:
        # 读取zhibo文件
        with open(zhibo_file, 'r', encoding='utf-8') as f:
            zhibo_data = json.load(f)
        
        print(f"✅ 成功读取zhibo文件，数据类型: {type(zhibo_data)}")
        print(f"   数据长度: {len(zhibo_data)}")
        
        # 模拟文本清理函数
        def clean_text_for_tts(text):
            if not text or not isinstance(text, str):
                return None
            text = text.strip()
            if len(text) < 3:  # 过滤太短的文本
                return None
            return text
        
        # 测试新的提取逻辑
        texts = []
        seen_texts = set()
        
        for item in zhibo_data:
            if isinstance(item, dict) and "list" in item:
                item_list = item["list"]
                
                if isinstance(item_list, dict):
                    for timestamp, text_content in item_list.items():
                        # 新格式: {"timestamp": {"txt": "xxx", "is_adv": "0"}}
                        if isinstance(text_content, dict) and "txt" in text_content:
                            text_value = text_content["txt"]
                            is_adv = text_content.get("is_adv", "0")
                            
                            cleaned_text = clean_text_for_tts(text_value)
                            if cleaned_text and cleaned_text not in seen_texts:
                                texts.append(cleaned_text)
                                seen_texts.add(cleaned_text)
                                # 调试信息：显示前几个文本的内容和广告标识
                                if len(texts) <= 5:
                                    adv_status = "广告" if is_adv == "1" else "普通"
                                    print(f"📝 提取文本 {len(texts)} ({adv_status}): {cleaned_text[:50]}...")
                        
                        # 兼容旧格式: {"timestamp": "text"}
                        elif isinstance(text_content, str):
                            cleaned_text = clean_text_for_tts(text_content)
                            if cleaned_text and cleaned_text not in seen_texts:
                                texts.append(cleaned_text)
                                seen_texts.add(cleaned_text)
                                # 调试信息：显示前几个文本的内容
                                if len(texts) <= 5:
                                    print(f"📝 提取文本 {len(texts)} (旧格式): {cleaned_text[:50]}...")
        
        print(f"✅ 提取到 {len(texts)} 条有效文案（已去重）")
        
        if len(texts) == 0:
            print("❌ 没有提取到任何文本，检查数据格式...")
            
            # 详细检查数据结构
            for i, item in enumerate(zhibo_data[:3]):  # 只检查前3个项目
                print(f"   项目 {i+1}: {type(item)}")
                if isinstance(item, dict):
                    print(f"      键: {list(item.keys())}")
                    if "list" in item:
                        item_list = item["list"]
                        print(f"      list类型: {type(item_list)}")
                        if isinstance(item_list, dict):
                            for timestamp, content in list(item_list.items())[:2]:  # 只显示前2个
                                print(f"         {timestamp}: {type(content)} - {str(content)[:50]}...")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始文本提取测试")
    print("=" * 50)
    
    if test_text_extraction():
        print("\n" + "=" * 50)
        print("✅ 测试通过！文本提取功能正常")
        return True
    else:
        print("\n" + "=" * 50)
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    main()
