#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TTS异步修复的脚本
"""
import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_async_fix():
    """测试异步修复"""
    print("🔍 测试TTS异步修复...")
    
    try:
        # 模拟创建AITxtTTSProcessor
        class MockMainController:
            def __init__(self):
                self.zhibo_cache_dir = "src/cache/zhibo"
                self.tts_plugin = None
                
        class MockTTSProcessor:
            def __init__(self, main_controller):
                self.main_controller = main_controller
                self.tts_plugin = main_controller.tts_plugin
                
            async def process_ai_tts_conversion(self, progress_callback=None):
                """模拟异步TTS转换"""
                if progress_callback:
                    progress_callback("开始转换...")
                
                # 模拟异步操作
                await asyncio.sleep(0.1)
                
                if progress_callback:
                    progress_callback("转换完成")
                
                return {
                    "success": True,
                    "statistics": {
                        "total_texts": 5,
                        "success_count": 5,
                        "error_count": 0,
                        "cache_hit_count": 2
                    },
                    "results": []
                }
            
            def process_ai_tts_async(self, completion_callback):
                """异步处理zhibo文案TTS转换"""
                def worker():
                    try:
                        # 进度回调函数
                        def progress_callback(message):
                            print(f"TTS转换: {message}")
                        
                        # 执行TTS转换 - 使用asyncio.run来正确处理异步方法
                        result = asyncio.run(self.process_ai_tts_conversion(progress_callback))
                        
                        # 模拟主线程回调
                        completion_callback(result)
                        
                    except Exception as e:
                        error_msg = f"TTS异步处理异常: {str(e)}"
                        print(f"❌ {error_msg}")
                        result = {"success": False, "error": error_msg}
                        completion_callback(result)
                
                # 启动工作线程
                import threading
                thread = threading.Thread(target=worker, daemon=True)
                thread.start()
                
                # 等待线程完成（仅用于测试）
                thread.join()
        
        # 创建模拟对象
        mock_controller = MockMainController()
        tts_processor = MockTTSProcessor(mock_controller)
        
        print("✅ 成功创建TTS处理器")
        
        # 测试异步调用
        result_received = None
        
        def completion_callback(result):
            nonlocal result_received
            result_received = result
            print(f"✅ 收到回调结果: {result}")
        
        print("🚀 开始测试异步调用...")
        tts_processor.process_ai_tts_async(completion_callback)
        
        # 检查结果
        if result_received is not None:
            if result_received.get("success"):
                print("✅ 异步调用成功，返回正确的结果字典")
                stats = result_received.get("statistics", {})
                print(f"   统计信息: {stats}")
                return True
            else:
                print(f"❌ 异步调用失败: {result_received.get('error')}")
                return False
        else:
            print("❌ 未收到回调结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始TTS异步修复测试")
    print("=" * 50)
    
    if test_async_fix():
        print("\n" + "=" * 50)
        print("✅ 测试通过！TTS异步修复成功")
        return True
    else:
        print("\n" + "=" * 50)
        print("❌ 测试失败")
        return False

if __name__ == "__main__":
    main()
