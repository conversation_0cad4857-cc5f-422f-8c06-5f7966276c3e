# 需求文档

## 介绍

删除系统中保存 adv_ai_data 的功能。当前系统在处理AI优化结果时会将原始数据保存到 adv_ai_data 字段中，这个功能需要被完全移除以简化代码结构和减少存储开销。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望移除 adv_ai_data 保存功能，以便简化代码结构并减少不必要的数据存储。

#### 验收标准

1. 当 AI 优化处理完成时，系统不应再保存原始数据到 adv_ai_data 字段
2. 当 AI 优化处理完成时，系统不应再调用 _save_ai_raw_data 方法
3. 如果 _save_ai_raw_data 方法被移除，则不应影响其他功能的正常运行

### 需求 2

**用户故事：** 作为开发者，我希望清理与 adv_ai_data 相关的所有代码，以便保持代码库的整洁。

#### 验收标准

1. 当代码清理完成时，系统中不应存在任何对 adv_ai_data 的引用
2. 当代码清理完成时，_save_ai_raw_data 方法应被完全移除
3. 当代码清理完成时，相关的调用代码应被移除或修改

### 需求 3

**用户故事：** 作为用户，我希望系统在移除 adv_ai_data 功能后仍能正常工作，以便不影响现有的AI优化流程。

#### 验收标准

1. 当 adv_ai_data 功能被移除后，AI 优化流程应继续正常工作
2. 当 adv_ai_data 功能被移除后，其他数据保存功能（如 adv_data）应不受影响
3. 当 adv_ai_data 功能被移除后，系统应能正常解析和处理AI返回的JSON数据