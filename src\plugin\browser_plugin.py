#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
import os
from playwright.async_api import async_playwright

class iPhone14LandscapeBrowserPlugin:
    def __init__(self):
        self.is_running = False
        self.last_error = None  # 保存最后一次错误信息       
        # 初始化 device_config - 基础 iPhone 14 Pro Max 横屏配置
        self.device_config = {
            'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'viewport': {'width': 932, 'height': 430},  # 横屏尺寸
            'device_scale_factor': 3,    # Super Retina XDR display
            'reduced_motion': 'no-preference',          # 动画设置
            'forced_colors': 'none',                    # 颜色设置
            'is_mobile': True,
            'has_touch': True,
            'extra_http_headers': {
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Cache-Control': 'max-age=0',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
            },
            # 绕过内容安全策略以支持音频播放
            'bypass_csp': True
        } 
        # 初始化浏览器相关属性
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
        self.browser_pid = None  # 浏览器进程ID
        # 刷新事件监听相关属性
        self.refresh_callback = None
        self.refresh_listener_active = False

    def find_chrome_executable(self):
        """查找Chrome可执行文件路径"""
        possible_paths = [
            # Windows paths
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    async def _force_close_browser_processes(self):
        """强制关闭浏览器进程"""
        try:
            import subprocess
            print("   🔨 开始强制关闭Playwright浏览器进程...")
            
            # 首先尝试通过命令行参数识别Playwright进程
            playwright_killed = False
            
            try:
                # 查找带有Playwright特征的浏览器进程
                result = subprocess.run([
                    'wmic', 'process', 'where', 
                    'commandline like "%--disable-blink-features=AutomationControlled%" or commandline like "%--exclude-switches=enable-automation%"',
                    'get', 'processid,name'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and result.stdout:
                    lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    for line in lines:
                        if line.strip():
                            parts = line.strip().split()
                            if len(parts) >= 2:
                                name = parts[0]
                                pid = parts[1]
                                try:
                                    print(f"   🎯 发现Playwright进程: {name} (PID: {pid})")
                                    subprocess.run(['taskkill', '/F', '/PID', pid], 
                                                 capture_output=True, timeout=5)
                                    print(f"   ✅ 已终止Playwright进程: {name} (PID: {pid})")
                                    playwright_killed = True
                                except Exception:
                                    pass
                    
                if not playwright_killed:
                    print("   ℹ️ 没有找到活跃的Playwright浏览器进程")
                else:
                    print("   ✅ Playwright浏览器进程清理完成")
                    
            except Exception as e:
                print(f"   ⚠️ 通过命令行识别进程失败: {e}")
                # 如果精确识别失败，回退到通用方式（但只针对可能的Playwright进程）
                print("   🔄 回退到通用清理方式...")
                
                # 只清理最近启动的浏览器进程（降低影响）
                try:
                    result = subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe', '/FI', 'CPUTIME gt 00:00:00'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print("   ✅ 清理了活跃的Chrome进程")
                except Exception:
                    pass
                    
                try:
                    result = subprocess.run(['taskkill', '/F', '/IM', 'msedge.exe', '/FI', 'CPUTIME gt 00:00:00'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print("   ✅ 清理了活跃的Edge进程")
                except Exception:
                    pass
            
            # 等待进程完全终止
            await asyncio.sleep(1)
            print("   ✅ 强制关闭浏览器进程完成")
            
        except Exception as e:
            print(f"   ❌ 强制关闭浏览器进程失败: {e}")
    
    async def start_browser(self, headless=False):
        try:    
            self.playwright = await async_playwright().start()
            chrome_path = self.find_chrome_executable()  
            self.browser = await self.playwright.chromium.launch(
                    executable_path=chrome_path,
                    devtools=True,
                    headless=headless,
                    args=[
                    '--auto-open-devtools-for-tabs',
                    '--exclude-switches=enable-automation',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--autoplay-policy=no-user-gesture-required',
                    '--disable-blink-features=AutomationControlled',
                    '--enable-touch-events',
                    '--enable-viewport-meta',
                    '--use-mobile-user-agent',
                    '--disable-extensions',
                    '--disable-translate',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-sync',
                    '--disable-default-apps',
                    '--disable-background-timer-throttling',
                    '--disable-popup-blocking',
                    '--disable-prompt-on-repost',
                    '--disable-hang-monitor',
                    '--disable-component-extensions-with-background-pages',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-infobars',
                    '--disable-notifications',
                    # 简化的音频播放权限相关参数
                    '--allow-autoplay-policy=no-user-gesture-required',
                    '--allow-insecure-localhost',
                    '--disable-gesture-requirement-for-media-playback',
                    '--enable-features=MediaSessionAPI',
                    ]
                )

            self.context = await self.browser.new_context(**self.device_config)
            
            # 创建页面对象
            self.page = await self.context.new_page()
 
            return True        
        except Exception as e:
            error_msg = f"浏览器启动失败: {type(e).__name__}: {str(e)}"
            print(f"❌ {error_msg}")
            self.last_error = error_msg
            return False
        
    async def navigate_to_url(self, url=None):     
        try:
            await self.page.goto(url, wait_until='networkidle', timeout=10000)
        except Exception as e:
            await self.page.goto(url, wait_until='domcontentloaded', timeout=40000)      
        await self.ensure_audio_permission()
  
    async def ensure_audio_permission(self):
        try:
            # 检查页面状态
            page_ready = await self.page.evaluate("() => document.readyState")
            # 如果页面还在加载，等待一下
            if page_ready == 'loading':
                await asyncio.sleep(0.5)
            await self.page.evaluate("""
                () => {
                    // 启用 AudioContext
                    if (window.AudioContext || window.webkitAudioContext) {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        console.log('🔊 AudioContext 创建成功, 初始状态:', audioContext.state);
                        
                        if (audioContext.state === 'suspended') {
                            console.log('🔊 AudioContext 被暂停，尝试恢复...');
                            audioContext.resume().then(() => {
                                console.log('🔊 AudioContext 恢复成功');
                            }).catch((e) => {
                                console.warn('🔊 AudioContext 恢复失败:', e);
                            });
                        }
                        console.log('🔊 AudioContext state:', audioContext.state);
                    } else {
                        console.warn('🔊 AudioContext 不可用');
                    }
                    
                    // 启用自动播放策略
                    try {
                        navigator.getUserMedia = navigator.getUserMedia || 
                                               navigator.webkitGetUserMedia || 
                                               navigator.mozGetUserMedia;
                        console.log('🔊 音频权限已启用');
                    } catch (e) {
                        console.warn('🔊 音频权限设置失败:', e);
                    }
                    
                    // 模拟用户交互
                    const clickEvent = new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    });
                    document.dispatchEvent(clickEvent);
                    console.log('🔊 模拟用户交互完成');
                    
                    console.log('🔊 音频权限注入脚本执行完成');
                    return '音频权限注入完成';
                }
            """)
        except Exception as e:
            import traceback
            traceback.print_exc() 
    
    async def start_live_stream(self, url=None):
        self.last_error = None  # 重置错误信息    
        try:
            self.is_running = True 
            await self.navigate_to_url(url)
        except Exception as e:
            error_msg = f"启动直播失败: {type(e).__name__}: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            print("❌ 详细错误信息:")
            traceback.print_exc()
            self.last_error = error_msg
            self.is_running = False
            return False
    
    async def close(self):
        try:
            print("🛑 正在关闭直播浏览器...")
            self.is_running = False
            
            # 记录是否需要强制关闭
            force_close_needed = False
            
            # 按照正确的顺序关闭资源
            try:
                # 1. 首先关闭页面
                if self.page and not self.page.is_closed():
                    await self.page.close()
                    print("   ✅ 页面已关闭")
                elif self.page:
                    print("   ℹ️ 页面已经关闭，跳过")
            except Exception as e:
                if "'NoneType' object has no attribute 'send'" in str(e):
                    print("   ℹ️ 页面连接已断开，需要强制关闭")
                    force_close_needed = True
                else:
                    print(f"   ⚠️ 关闭页面时出错: {e}")
            
            try:
                # 2. 关闭浏览器上下文
                if self.context:
                    await self.context.close()
                    print("   ✅ 浏览器上下文已关闭")
            except Exception as e:
                if "'NoneType' object has no attribute 'send'" in str(e):
                    print("   ℹ️ 浏览器上下文连接已断开，需要强制关闭")
                    force_close_needed = True
                else:
                    print(f"   ⚠️ 关闭浏览器上下文时出错: {e}")
            
            try:
                # 3. 关闭浏览器实例
                if self.browser:
                    await self.browser.close()
                    print("   ✅ 浏览器实例已关闭")
            except Exception as e:
                if "'NoneType' object has no attribute 'send'" in str(e):
                    print("   ℹ️ 浏览器实例连接已断开，需要强制关闭")
                    force_close_needed = True
                else:
                    print(f"   ⚠️ 关闭浏览器实例时出错: {e}")
            
            try:
                # 4. 最后停止 Playwright（检查事件循环状态）
                if self.playwright:
                    # 检查事件循环是否仍然可用
                    try:
                        loop = asyncio.get_event_loop()
                        if not loop.is_closed():
                            await self.playwright.stop()
                            print("   ✅ Playwright 已停止")
                        else:
                            print("   ℹ️ 事件循环已关闭，跳过 Playwright 停止")
                    except RuntimeError as e:
                        if "no current event loop" in str(e).lower():
                            print("   ℹ️ 没有当前事件循环，跳过 Playwright 停止")
                        else:
                            raise e
            except Exception as e:
                if "event loop is closed" in str(e).lower():
                    print("   ℹ️ 事件循环已关闭，Playwright 自动停止")
                elif "'NoneType' object has no attribute 'send'" in str(e):
                    print("   ℹ️ Playwright 连接已断开，需要强制关闭")
                    force_close_needed = True
                else:
                    print(f"   ⚠️ 停止 Playwright 时出错: {e}")
            
            # 5. 如果需要强制关闭，终止浏览器进程
            if force_close_needed:
                await self._force_close_browser_processes()
            
            # 清空引用
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            
            print("✅ 直播浏览器已关闭")
            return True
            
        except Exception as e:
            print(f"❌ 关闭浏览器失败: {e}")
            # 确保清空引用，即使关闭失败
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            return False

    async def check_connection_health(self):
        """检查浏览器连接是否健康"""
        try:
            # 首先检查页面对象是否存在或已关闭
            if not self.page or self.page.is_closed():
                print("   ❌ 页面对象不存在或已关闭")
                return False
            # 使用 evaluate 执行一个无害的JS表达式来测试连接
            await self.page.evaluate("() => 1")
            return True
        except Exception as e:
            # 明确捕获特征性断连错误
            if "'NoneType' object has no attribute 'send'" in str(e):
                print(f"   ❌ 检测到连接断开 (NoneType error)")
            else:
                print(f"   ❌ 连接健康检查失败: {type(e).__name__}: {e}")
            return False
