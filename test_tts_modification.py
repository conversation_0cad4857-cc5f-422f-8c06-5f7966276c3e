#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TTS修改后的数据源功能
"""
import os
import json
import sys

# 添加项目路径
sys.path.append('.')

def test_zhibo_file_reading():
    """测试zhibo文件读取功能"""
    print("=== 测试zhibo文件读取功能 ===")
    
    # 构建文件路径
    zhibo_file = os.path.join('src', 'cache', 'zhibo', 'a3ee86ff033eb4c6556150453704f738.json')
    print(f"文件路径: {zhibo_file}")
    print(f"文件存在: {os.path.exists(zhibo_file)}")
    
    if not os.path.exists(zhibo_file):
        print("❌ 文件不存在，测试失败")
        return False
    
    try:
        # 读取文件
        with open(zhibo_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"数据类型: {type(data)}")
        print(f"数据长度: {len(data)}")
        
        # 提取文本
        texts = []
        for item in data:
            if isinstance(item, dict) and 'list' in item:
                item_list = item['list']
                if isinstance(item_list, dict):
                    for timestamp, text_content in item_list.items():
                        if isinstance(text_content, str):
                            # 简单清理文本
                            cleaned_text = text_content.strip()
                            if cleaned_text and len(cleaned_text) > 10:  # 过滤太短的文本
                                texts.append(cleaned_text)
        
        print(f"提取到的文本数量: {len(texts)}")
        
        if len(texts) > 0:
            print(f"第一个文本: {texts[0][:100]}...")
            print(f"最后一个文本: {texts[-1][:100]}...")
            print("✅ zhibo文件读取测试成功")
            return True
        else:
            print("❌ 没有提取到有效文本")
            return False
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def test_aitxt_processor():
    """测试修改后的AITxtTTSProcessor"""
    print("\n=== 测试AITxtTTSProcessor修改 ===")
    
    try:
        # 模拟主控制器
        class MockController:
            def __init__(self):
                src_dir = os.path.dirname(os.path.abspath(__file__))
                self.zhibo_cache_dir = os.path.join(src_dir, "src", "cache", "zhibo")
        
        # 创建模拟的TTS处理器
        from src.page.txt.aitxt import AITxtTTSProcessor
        
        mock_controller = MockController()
        processor = AITxtTTSProcessor(mock_controller)
        
        # 测试文本提取方法
        texts = processor._extract_texts_from_ai_optimized()
        
        print(f"提取到的文本数量: {len(texts)}")
        
        if len(texts) > 0:
            print(f"第一个文本: {texts[0][:100]}...")
            print(f"最后一个文本: {texts[-1][:100]}...")
            print("✅ AITxtTTSProcessor修改测试成功")
            return True
        else:
            print("❌ AITxtTTSProcessor没有提取到有效文本")
            return False
            
    except Exception as e:
        print(f"❌ AITxtTTSProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试TTS数据源修改...")
    
    # 测试1: 直接文件读取
    test1_result = test_zhibo_file_reading()
    
    # 测试2: AITxtTTSProcessor修改
    test2_result = test_aitxt_processor()
    
    print(f"\n=== 测试结果 ===")
    print(f"zhibo文件读取: {'✅ 成功' if test1_result else '❌ 失败'}")
    print(f"AITxtTTSProcessor: {'✅ 成功' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！TTS数据源修改成功！")
    else:
        print("⚠️ 部分测试失败，请检查修改")
