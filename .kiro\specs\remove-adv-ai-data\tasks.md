# 实施计划

- [ ] 1. 移除 _save_ai_raw_data 方法
  - 删除 `src/page/txt/txtpage.py` 文件中的 `_save_ai_raw_data` 方法（第1618-1642行）
  - 确保方法定义和所有相关代码被完全移除
  - _需求: 2.2_

- [ ] 2. 移除对 _save_ai_raw_data 方法的调用
  - 在 `_parse_complete_optimization_result` 方法中删除 `self._save_ai_raw_data(result)` 调用（第1558行）
  - 删除相关注释 "# 1. 首先保存原始数据到 adv_ai_data 字段"（第1557行）
  - _需求: 1.1, 1.2_

- [ ] 3. 验证代码清理的完整性
  - 在整个代码库中搜索所有对 `adv_ai_data` 的引用
  - 确认没有遗留的相关代码或注释
  - 验证没有其他地方调用已删除的 `_save_ai_raw_data` 方法
  - _需求: 2.1, 2.3_

- [ ] 4. 测试AI优化流程的完整性
  - 运行AI优化功能，验证流程能正常工作
  - 确认 `_save_ai_raw_response` 功能仍然正常
  - 确认 `_extract_json_from_response` 功能仍然正常
  - 确认AI优化结果能正常显示
  - _需求: 3.1_

- [ ] 5. 验证缓存文件结构
  - 触发AI优化功能生成新的缓存文件
  - 检查新生成的缓存文件不包含 `adv_ai_data` 字段
  - 验证其他字段（如 `adv_data`）的保存功能正常
  - _需求: 3.2_

- [ ] 6. 测试向后兼容性
  - 使用包含 `adv_ai_data` 字段的旧缓存文件测试系统
  - 验证系统能正常读取和处理旧格式的缓存文件
  - 确认系统不会因为缺少 `adv_ai_data` 字段而出错
  - _需求: 3.1, 3.3_