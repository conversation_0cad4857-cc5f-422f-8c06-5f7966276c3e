#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直播控制台启动脚本
请从项目根目录运行此脚本
"""

import sys
import os

# 确保当前目录是脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 运行主程序
if __name__ == "__main__":
    # 使用 -m 参数运行模块
    import subprocess
    try:
        # 使用当前 Python 解释器运行
        result = subprocess.run(
            [sys.executable, "-m", "src.integrated_launcher"],
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"❌ 程序启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 程序已停止")
        sys.exit(0)
