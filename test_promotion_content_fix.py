#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推广文案优化内容在主播稿生成中的修复
"""
import os
import sys
import json

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_promotion_content_in_zhubo():
    """测试推广文案优化内容在主播稿生成中是否正确包含"""
    print("🧪 测试推广文案优化内容在主播稿生成中的修复")
    print("=" * 60)
    
    # 模拟包含推广文案优化数据的AI优化数据
    mock_ai_data = {
        "optimized_content": [
            {
                "list": {
                    "10.5": "这是第一段优化内容",
                    "15.2": "这是第二段优化内容"
                }
            }
        ],
        "instr": "欢迎大家来到直播间，今天我们来学习科目三",
        "end": "感谢大家的观看，我们下次再见",
        # 原始推广文案数据（空内容）
        "adv_data": [
            {
                "list": {
                    "20.5": {
                        "txt": "",  # 原始为空
                        "is_adv": "1"
                    },
                    "25.8": {
                        "txt": "",  # 原始为空
                        "is_adv": "1"
                    }
                }
            }
        ],
        # 优化后的推广文案数据（JSON字符串格式）
        "generated_content": json.dumps([
            {
                "list": {
                    "20.5": {
                        "txt": "限时优惠！科目三通关秘籍，今天报名立减200元！",
                        "is_adv": "1"
                    },
                    "25.8": {
                        "txt": "最后机会！错过今天就没有这个价格了，赶紧行动！",
                        "is_adv": "1"
                    }
                }
            }
        ], ensure_ascii=False)
    }
    
    try:
        # 直接导入zhubo模块
        import importlib.util
        zhubo_spec = importlib.util.spec_from_file_location("zhubo", os.path.join(src_dir, "page", "txt", "zhubo.py"))
        zhubo_module = importlib.util.module_from_spec(zhubo_spec)
        zhubo_spec.loader.exec_module(zhubo_module)
        ZhuboDataProcessor = zhubo_module.ZhuboDataProcessor
        
        # 创建模拟的主控制器
        class MockController:
            def __init__(self):
                self.ai_optimized_data = {}
                self.root = None  # 添加root属性
                
        mock_controller = MockController()
        zhubo_processor = ZhuboDataProcessor(mock_controller)
        
        print("✅ 成功导入ZhuboDataProcessor")
        
        # 测试生成主播稿数据
        print("\n📊 测试生成主播稿数据（包含推广文案优化内容）...")
        zhubo_data = zhubo_processor._generate_zhubo_data(mock_ai_data)
        
        print(f"✅ 生成主播稿数据成功，包含 {len(zhubo_data)} 个项目")
        
        # 验证推广文案优化内容是否正确包含
        print("\n🔍 验证推广文案优化内容...")
        
        promotion_items = []
        all_content = []
        
        for i, item in enumerate(zhubo_data):
            if isinstance(item, dict) and "list" in item:
                project_list = item["list"]
                for timestamp, content in project_list.items():
                    all_content.append({
                        "项目索引": i,
                        "时间戳": timestamp,
                        "内容": content,
                        "类型": "开场白" if timestamp == "0" and content.get("is_adv") == "0" 
                               else "退场白" if timestamp == "2000" and content.get("is_adv") == "0"
                               else "推广文案" if content.get("is_adv") == "1"
                               else "优化内容"
                    })
                    
                    if content.get("is_adv") == "1":
                        promotion_items.append({
                            "时间戳": timestamp,
                            "内容": content.get("txt", ""),
                            "来源": content.get("source", "未知")
                        })
        
        print(f"📊 统计结果:")
        print(f"   总内容项: {len(all_content)}")
        print(f"   推广文案项: {len(promotion_items)}")
        
        # 检查推广文案内容是否为空
        empty_promotions = [item for item in promotion_items if not item["内容"].strip()]
        filled_promotions = [item for item in promotion_items if item["内容"].strip()]
        
        print(f"   空推广文案: {len(empty_promotions)}")
        print(f"   有内容推广文案: {len(filled_promotions)}")
        
        # 显示推广文案内容
        if filled_promotions:
            print("\n✅ 推广文案优化内容已正确包含:")
            for item in filled_promotions:
                print(f"   时间戳 {item['时间戳']}: {item['内容'][:50]}...")
                print(f"   来源: {item['来源']}")
        else:
            print("\n❌ 推广文案优化内容丢失!")
            if empty_promotions:
                print("   发现空的推广文案项:")
                for item in empty_promotions:
                    print(f"   时间戳 {item['时间戳']}: (空内容)")
        
        # 输出完整的主播稿数据结构
        print("\n📄 完整的主播稿数据结构:")
        print(json.dumps(zhubo_data, indent=2, ensure_ascii=False))
        
        # 保存测试结果
        test_result_file = "test_promotion_content_fix_result.json"
        with open(test_result_file, 'w', encoding='utf-8') as f:
            json.dump(zhubo_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 测试结果已保存到: {test_result_file}")
        
        # 返回测试结果
        return len(filled_promotions) > 0 and len(empty_promotions) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_promotion_content_in_zhubo()
    if success:
        print("\n🎉 推广文案优化内容修复测试通过！")
    else:
        print("\n❌ 推广文案优化内容修复测试失败！")
