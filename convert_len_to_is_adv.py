#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将推广文案数据中的len字段转换为is_adv字段
"""

import json
import os

def convert_len_to_is_adv():
    """将推广文案数据中的len字段转换为is_adv字段"""
    
    cache_file = "src/cache/ai_optimized/ai_a3ee86ff033eb4c6556150453704f738.json"
    
    if not os.path.exists(cache_file):
        print(f"❌ 缓存文件不存在: {cache_file}")
        return
    
    print(f"🔧 开始转换推广文案数据格式: {cache_file}")
    
    # 读取原始数据
    with open(cache_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 转换函数
    def convert_project_list(project_list):
        """转换项目列表中的len字段为is_adv字段"""
        converted_list = {}
        converted_count = 0
        
        for timestamp, content in project_list.items():
            if isinstance(content, dict) and "len" in content and "txt" in content:
                # 转换格式：移除len字段，添加is_adv字段
                converted_list[timestamp] = {
                    "txt": content["txt"],
                    "is_adv": "1"
                }
                converted_count += 1
                print(f"🔄 转换时间戳 {timestamp}: len -> is_adv")
            else:
                # 保持原格式
                converted_list[timestamp] = content
        
        return converted_list, converted_count
    
    total_converted = 0
    
    # 转换adv_org字段
    if "adv_org" in data:
        print("📊 转换adv_org字段...")
        converted_adv_org = []
        for project in data["adv_org"]:
            if isinstance(project, dict) and "list" in project:
                converted_list, count = convert_project_list(project["list"])
                converted_adv_org.append({"list": converted_list})
                total_converted += count
            else:
                converted_adv_org.append(project)
        data["adv_org"] = converted_adv_org
    
    # 转换adv_data字段
    if "adv_data" in data:
        print("📊 转换adv_data字段...")
        converted_adv_data = []
        for project in data["adv_data"]:
            if isinstance(project, dict) and "list" in project:
                converted_list, count = convert_project_list(project["list"])
                converted_adv_data.append({"list": converted_list})
                total_converted += count
            else:
                converted_adv_data.append(project)
        data["adv_data"] = converted_adv_data
    
    # 备份原文件
    backup_file = cache_file + ".len_backup"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"📝 原文件已备份到: {backup_file}")
    
    # 保存转换后的数据
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 转换完成！共转换 {total_converted} 个len字段为is_adv字段")
    print(f"📄 转换后的数据已保存到: {cache_file}")
    
    # 验证转换结果
    print("\n🔍 验证转换结果...")
    with open(cache_file, 'r', encoding='utf-8') as f:
        verify_data = json.load(f)
    
    len_count = 0
    is_adv_count = 0
    
    def count_fields(project_list):
        nonlocal len_count, is_adv_count
        for timestamp, content in project_list.items():
            if isinstance(content, dict):
                if "len" in content:
                    len_count += 1
                if "is_adv" in content:
                    is_adv_count += 1
    
    # 统计adv_org字段
    if "adv_org" in verify_data:
        for project in verify_data["adv_org"]:
            if isinstance(project, dict) and "list" in project:
                count_fields(project["list"])
    
    # 统计adv_data字段
    if "adv_data" in verify_data:
        for project in verify_data["adv_data"]:
            if isinstance(project, dict) and "list" in project:
                count_fields(project["list"])
    
    print(f"📊 验证结果:")
    print(f"   剩余len字段: {len_count}")
    print(f"   is_adv字段: {is_adv_count}")
    
    if len_count == 0:
        print("✅ 所有len字段已成功转换为is_adv字段！")
    else:
        print(f"⚠️ 还有 {len_count} 个len字段未转换")

if __name__ == "__main__":
    convert_len_to_is_adv()
