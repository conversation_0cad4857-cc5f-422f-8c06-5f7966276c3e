#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MD5即时编码语音文件查找功能
验证删除voice_data调用，通过即时编码查找MP3文件的可行性
"""
import os
import sys
import json
import hashlib
from typing import Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.utils.tts_hash_base import TTSHashBase

class VoiceLookupTester:
    """语音文件查找测试器"""
    
    def __init__(self):
        self.hash_generator = TTSHashBase()
        self.zhibo_cache_dir = os.path.join(project_root, 'src', 'cache', 'zhibo')
        self.voice_cache_dir = os.path.join(project_root, 'src', 'cache', 'voice')
        
    def load_test_data(self) -> Dict[str, Any]:
        """加载测试数据"""
        try:
            # 查找最新的voice缓存文件
            voice_files = [f for f in os.listdir(self.zhibo_cache_dir) 
                          if f.startswith('voice_') and f.endswith('.json')]
            
            if not voice_files:
                print("❌ 未找到voice缓存文件")
                return {}
            
            # 使用最新的文件
            latest_voice_file = max(voice_files, 
                                  key=lambda f: os.path.getmtime(os.path.join(self.zhibo_cache_dir, f)))
            voice_file_path = os.path.join(self.zhibo_cache_dir, latest_voice_file)
            
            print(f"📁 使用voice缓存文件: {latest_voice_file}")
            
            # 加载voice数据
            with open(voice_file_path, 'r', encoding='utf-8') as f:
                voice_data = json.load(f)
            
            # 加载对应的主播稿数据
            cache_key = voice_data.get('cache_key', '')
            if cache_key:
                zhubo_file = f"{cache_key}.json"
                zhubo_file_path = os.path.join(self.zhibo_cache_dir, zhubo_file)
                
                if os.path.exists(zhubo_file_path):
                    with open(zhubo_file_path, 'r', encoding='utf-8') as f:
                        zhubo_data = json.load(f)
                    
                    print(f"📖 加载主播稿数据: {zhubo_file}")
                    return {
                        'voice_data': voice_data.get('voice_data', {}),
                        'zhubo_data': zhubo_data,
                        'cache_key': cache_key
                    }
            
            print("⚠️ 未找到对应的主播稿数据")
            return {'voice_data': voice_data.get('voice_data', {})}
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")
            return {}
    
    def extract_texts_from_zhubo_data(self, zhubo_data: list) -> Dict[str, str]:
        """从主播稿数据中提取文本内容"""
        texts = {}
        
        for item in zhubo_data:
            if not isinstance(item, dict) or 'list' not in item:
                continue
            
            item_list = item['list']
            for time_key, content in item_list.items():
                if isinstance(content, dict) and 'txt' in content:
                    text = content['txt']
                    if text and text.strip():
                        texts[time_key] = text.strip()
        
        return texts
    
    def test_md5_voice_lookup(self):
        """测试MD5即时编码语音文件查找"""
        print("🧪 开始测试MD5即时编码语音文件查找...")
        print("=" * 60)
        
        # 加载测试数据
        test_data = self.load_test_data()
        if not test_data:
            return False
        
        voice_data = test_data.get('voice_data', {})
        zhubo_data = test_data.get('zhubo_data', [])
        
        if not voice_data:
            print("❌ 没有voice_data数据")
            return False
        
        if not zhubo_data:
            print("⚠️ 没有主播稿数据，只能测试现有voice_data")
            return self.test_existing_voice_data(voice_data)
        
        # 提取原始文本
        original_texts = self.extract_texts_from_zhubo_data(zhubo_data)
        print(f"📝 提取到 {len(original_texts)} 条原始文本")
        print(f"🎵 voice_data包含 {len(voice_data)} 条语音映射")
        
        # 测试统计
        total_tests = 0
        success_count = 0
        md5_match_count = 0
        file_exists_count = 0
        
        print("\n🔍 开始逐条测试...")
        print("-" * 60)
        
        for time_str, expected_path in list(voice_data.items())[:10]:  # 测试前10条
            total_tests += 1
            print(f"\n📍 测试时间点: {time_str}")
            
            # 检查是否有对应的原始文本
            if time_str not in original_texts:
                print(f"   ⚠️ 未找到对应的原始文本")
                continue
            
            text_content = original_texts[time_str]
            print(f"   📄 原始文本: {text_content[:50]}...")
            
            # 通过MD5即时编码生成路径
            generated_path = self.hash_generator._generate_text_path(text_content)
            print(f"   🔗 预期路径: {expected_path}")
            print(f"   🔗 生成路径: {generated_path}")
            
            # 检查路径是否匹配
            if os.path.normpath(expected_path) == os.path.normpath(generated_path):
                md5_match_count += 1
                print(f"   ✅ MD5路径匹配")
            else:
                print(f"   ❌ MD5路径不匹配")
            
            # 检查文件是否存在
            if os.path.exists(generated_path):
                file_exists_count += 1
                print(f"   ✅ 文件存在")
                success_count += 1
            else:
                print(f"   ❌ 文件不存在")
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   MD5路径匹配: {md5_match_count}/{total_tests} ({md5_match_count/total_tests*100:.1f}%)")
        print(f"   文件存在: {file_exists_count}/{total_tests} ({file_exists_count/total_tests*100:.1f}%)")
        print(f"   整体成功: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
        
        # 可行性评估
        if success_count == total_tests:
            print("\n🎉 结论: MD5即时编码完全可行！")
            print("   ✅ 所有测试都通过，可以安全删除voice_data调用")
        elif success_count >= total_tests * 0.8:
            print("\n✅ 结论: MD5即时编码基本可行")
            print("   ⚠️ 大部分测试通过，建议进一步优化后使用")
        else:
            print("\n❌ 结论: MD5即时编码存在问题")
            print("   🔧 需要检查MD5生成逻辑或文件存储机制")
        
        return success_count == total_tests
    
    def test_existing_voice_data(self, voice_data: Dict[str, str]):
        """测试现有voice_data的文件存在性"""
        print("🔍 测试现有voice_data的文件存在性...")
        
        total_files = len(voice_data)
        existing_files = 0
        
        for time_str, file_path in voice_data.items():
            if os.path.exists(file_path):
                existing_files += 1
        
        print(f"📊 文件存在性: {existing_files}/{total_files} ({existing_files/total_files*100:.1f}%)")
        return existing_files == total_files

def main():
    """主函数"""
    print("🚀 MD5即时编码语音文件查找可行性测试")
    print("=" * 60)
    
    tester = VoiceLookupTester()
    success = tester.test_md5_voice_lookup()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 测试完成: 方案完全可行！")
    else:
        print("⚠️ 测试完成: 需要进一步优化")

if __name__ == "__main__":
    main()
