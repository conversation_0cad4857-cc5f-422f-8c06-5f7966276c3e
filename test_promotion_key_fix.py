#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推广文案键值修复 - 确保推广文案保持原始时间戳
"""
import os
import sys
import json

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_promotion_key_preservation():
    """测试推广文案键值保持原始时间戳"""
    print("🧪 测试推广文案键值修复 - 保持原始时间戳")
    print("=" * 60)
    
    # 模拟包含各种时间戳的推广文案数据，包括可能冲突的 "0" 和 "2000"
    mock_ai_data = {
        "optimized_content": [
            {
                "list": {
                    "10.5": "这是第一段优化内容",
                    "15.2": "这是第二段优化内容"
                }
            }
        ],
        "instr": "欢迎大家来到直播间，今天我们来学习科目三",
        "end": "感谢大家的观看，我们下次再见",
        # 推广文案数据包含各种时间戳，包括 "0" 和 "2000"
        "generated_content": json.dumps([
            {
                "list": {
                    "0": {  # 这个时间戳应该保持为 "0"，不被修改
                        "txt": "今天开播就一个目的——把科三路考的通关干货给你们掰开揉碎讲透！",
                        "is_adv": "1"
                    },
                    "25.8": {
                        "txt": "限时优惠！科目三通关秘籍，今天报名立减200元！",
                        "is_adv": "1"
                    },
                    "2000": {  # 这个时间戳应该保持为 "2000"，不被修改
                        "txt": "最后机会！错过今天就没有这个价格了，赶紧行动！",
                        "is_adv": "1"
                    }
                }
            }
        ], ensure_ascii=False)
    }
    
    try:
        # 直接导入zhubo模块
        import importlib.util
        zhubo_spec = importlib.util.spec_from_file_location("zhubo", os.path.join(src_dir, "page", "txt", "zhubo.py"))
        zhubo_module = importlib.util.module_from_spec(zhubo_spec)
        zhubo_spec.loader.exec_module(zhubo_module)
        ZhuboDataProcessor = zhubo_module.ZhuboDataProcessor
        
        # 创建模拟的主控制器
        class MockController:
            def __init__(self):
                self.ai_optimized_data = {}
                self.root = None
                
        mock_controller = MockController()
        zhubo_processor = ZhuboDataProcessor(mock_controller)
        
        print("✅ 成功导入ZhuboDataProcessor")
        
        # 测试生成主播稿数据
        print("\n📊 测试生成主播稿数据（包含可能冲突的时间戳）...")
        zhubo_data = zhubo_processor._generate_zhubo_data(mock_ai_data)
        
        print(f"✅ 生成主播稿数据成功，包含 {len(zhubo_data)} 个项目")
        
        # 验证键值是否正确保持
        print("\n🔍 验证键值保持情况...")
        
        all_keys = []
        key_analysis = {
            "opening_key": None,
            "ending_key": None,
            "promotion_keys": [],
            "optimized_keys": [],
            "key_conflicts": []
        }
        
        for i, item in enumerate(zhubo_data):
            if isinstance(item, dict) and "list" in item:
                project_list = item["list"]
                for timestamp, content in project_list.items():
                    all_keys.append(timestamp)
                    
                    # 分析键值类型
                    if content.get("source") == "开场白":
                        key_analysis["opening_key"] = timestamp
                    elif content.get("source") == "退场白":
                        key_analysis["ending_key"] = timestamp
                    elif content.get("is_adv") == "1":
                        key_analysis["promotion_keys"].append(timestamp)
                    elif content.get("is_adv") == "0":
                        key_analysis["optimized_keys"].append(timestamp)
        
        # 检查重复键值
        key_counts = {}
        for key in all_keys:
            key_counts[key] = key_counts.get(key, 0) + 1
        
        duplicate_keys = [key for key, count in key_counts.items() if count > 1]
        key_analysis["key_conflicts"] = duplicate_keys
        
        # 输出分析结果
        print(f"📊 键值分析结果:")
        print(f"   开场白键值: {key_analysis['opening_key']}")
        print(f"   退场白键值: {key_analysis['ending_key']}")
        print(f"   推广文案键值: {key_analysis['promotion_keys']}")
        print(f"   优化内容键值: {key_analysis['optimized_keys']}")
        print(f"   重复键值: {key_analysis['key_conflicts']}")
        
        # 验证修复效果
        success_checks = []
        
        # 检查1: 开场白使用特殊键值
        if key_analysis["opening_key"] == "opening":
            print("✅ 开场白正确使用特殊键值 'opening'")
            success_checks.append(True)
        else:
            print(f"❌ 开场白键值错误: {key_analysis['opening_key']} (应为 'opening')")
            success_checks.append(False)
        
        # 检查2: 退场白使用特殊键值
        if key_analysis["ending_key"] == "ending":
            print("✅ 退场白正确使用特殊键值 'ending'")
            success_checks.append(True)
        else:
            print(f"❌ 退场白键值错误: {key_analysis['ending_key']} (应为 'ending')")
            success_checks.append(False)
        
        # 检查3: 推广文案保持原始时间戳
        expected_promotion_keys = ["0", "25.8", "2000"]
        if set(key_analysis["promotion_keys"]) == set(expected_promotion_keys):
            print("✅ 推广文案正确保持原始时间戳")
            success_checks.append(True)
        else:
            print(f"❌ 推广文案时间戳错误: {key_analysis['promotion_keys']} (应为 {expected_promotion_keys})")
            success_checks.append(False)
        
        # 检查4: 无重复键值
        if not key_analysis["key_conflicts"]:
            print("✅ 无重复键值冲突")
            success_checks.append(True)
        else:
            print(f"❌ 发现重复键值: {key_analysis['key_conflicts']}")
            success_checks.append(False)
        
        # 输出完整的主播稿数据结构
        print("\n📄 完整的主播稿数据结构:")
        print(json.dumps(zhubo_data, indent=2, ensure_ascii=False))
        
        # 保存测试结果
        test_result_file = "test_promotion_key_fix_result.json"
        with open(test_result_file, 'w', encoding='utf-8') as f:
            json.dump(zhubo_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 测试结果已保存到: {test_result_file}")
        
        # 返回测试结果
        return all(success_checks)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_promotion_key_preservation()
    if success:
        print("\n🎉 推广文案键值修复测试通过！")
    else:
        print("\n❌ 推广文案键值修复测试失败！")
