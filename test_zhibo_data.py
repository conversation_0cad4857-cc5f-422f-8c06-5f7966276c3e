#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试zhibo数据读取功能
"""
import os
import json

def test_zhibo_data():
    """测试zhibo文件数据读取"""
    # 测试zhibo文件路径和数据读取
    zhibo_file = os.path.join('src', 'cache', 'zhibo', 'a3ee86ff033eb4c6556150453704f738.json')
    print(f'Zhibo文件路径: {zhibo_file}')
    print(f'文件是否存在: {os.path.exists(zhibo_file)}')

    if os.path.exists(zhibo_file):
        with open(zhibo_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f'数据类型: {type(data)}')
        print(f'数据长度: {len(data)}')
        
        # 测试文本提取逻辑
        texts = []
        for item in data:
            if isinstance(item, dict) and 'list' in item:
                item_list = item['list']
                if isinstance(item_list, dict):
                    for timestamp, text_content in item_list.items():
                        if isinstance(text_content, str):
                            texts.append(text_content)
        
        print(f'提取到的文本数量: {len(texts)}')
        if len(texts) > 0:
            print(f'第一个文本: {texts[0][:100]}...')
            print(f'最后一个文本: {texts[-1][:100]}...')
    else:
        print("❌ zhibo文件不存在")

if __name__ == "__main__":
    test_zhibo_data()
