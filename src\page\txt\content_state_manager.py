#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Content State Manager Mo<PERSON>le - Manages the state and transitions between original and optimized content
"""
from typing import Tuple, Optional
from datetime import datetime


class ContentStateManager:
    """
    Manages the state and transitions between original and optimized AI-generated content.
    
    This class tracks content states and provides methods for managing content transitions
    in the AI copywriting split functionality.
    """
    
    def __init__(self, main_controller):
        """
        Initialize the ContentStateManager.
        
        Args:
            main_controller: Reference to the main application controller
        """
        self.main_controller = main_controller
        self.original_content: Optional[str] = None
        self.optimized_content: Optional[str] = None
        self.current_state: str = "empty"  # empty, original_generated, optimized
        self.original_timestamp: Optional[str] = None
        self.optimized_timestamp: Optional[str] = None
    
    def set_original_content(self, content: str) -> None:
        """
        Set original content and update state.
        
        Args:
            content: The original AI-generated content
        """
        if not isinstance(content, str):
            raise ValueError("Content must be a string")
        
        self.original_content = content.strip()
        self.original_timestamp = datetime.now().isoformat()
        
        # Update state based on content availability
        if self.original_content:
            self.current_state = "original_generated"
            # Clear optimized content when new original content is set
            self.optimized_content = None
            self.optimized_timestamp = None
        else:
            self.current_state = "empty"
    
    def set_optimized_content(self, content: str) -> None:
        """
        Set optimized content and update state.
        
        Args:
            content: The optimized AI content
        """
        if not isinstance(content, str):
            raise ValueError("Content must be a string")
        
        # Can only set optimized content if original content exists
        if not self.original_content:
            raise ValueError("Cannot set optimized content without original content")
        
        self.optimized_content = content.strip()
        self.optimized_timestamp = datetime.now().isoformat()
        
        # Update state
        if self.optimized_content:
            self.current_state = "optimized"
        else:
            self.current_state = "original_generated"
    
    def get_current_content(self) -> Tuple[Optional[str], Optional[str]]:
        """
        Return current content state.
        
        Returns:
            Tuple of (original_content, optimized_content)
        """
        return (self.original_content, self.optimized_content)
    
    def reset_content(self) -> None:
        """
        Clear all content and reset state.
        """
        self.original_content = None
        self.optimized_content = None
        self.original_timestamp = None
        self.optimized_timestamp = None
        self.current_state = "empty"
    
    def has_original_content(self) -> bool:
        """
        Check if original content exists.
        
        Returns:
            True if original content is available
        """
        return self.original_content is not None and len(self.original_content.strip()) > 0
    
    def has_optimized_content(self) -> bool:
        """
        Check if optimized content exists.
        
        Returns:
            True if optimized content is available
        """
        return self.optimized_content is not None and len(self.optimized_content.strip()) > 0
    
    def can_optimize(self) -> bool:
        """
        Check if content can be optimized.
        
        Returns:
            True if original content exists and optimization is possible
        """
        return self.has_original_content()
    
    def get_state(self) -> str:
        """
        Get current state.
        
        Returns:
            Current state string: "empty", "original_generated", or "optimized"
        """
        return self.current_state
    
    def get_content_info(self) -> dict:
        """
        Get detailed content information.
        
        Returns:
            Dictionary containing content details and metadata
        """
        return {
            "state": self.current_state,
            "has_original": self.has_original_content(),
            "has_optimized": self.has_optimized_content(),
            "can_optimize": self.can_optimize(),
            "original_length": len(self.original_content) if self.original_content else 0,
            "optimized_length": len(self.optimized_content) if self.optimized_content else 0,
            "original_timestamp": self.original_timestamp,
            "optimized_timestamp": self.optimized_timestamp
        }