# -*- coding: utf-8 -*-
"""
阿里云语音合成插件
使用CosyVoice-v2模型进行异步语音合成
支持智能缓存功能
"""
import os
import asyncio
import threading
from datetime import datetime
from typing import Dict, Any, Optional
import dashscope
from dashscope.audio.tts_v2 import SpeechSynthesizer, ResultCallback
from src.utils.tts_hash_base import TTSHashBase


class TTSPlugin(TTSHashBase):
    """阿里云语音合成插件（带缓存功能）"""
    
    def __init__(self):
        """
        初始化语音合成插件
        """
        # 调用基类初始化方法
        super().__init__()
        # TTSHashBase 加载配置文件
        self.api_key = self._cache_config['api_key']
        # 设置dashscope的API key
        dashscope.api_key = self.api_key 

    def _check_cache_exists(self, text: str) -> Optional[Dict[str, Any]]:
        """检查是否有可用的缓存 - 直接通过文件系统查找"""
        try:
            cache_file = self._generate_text_path(text)
            file_size = os.path.getsize(cache_file)
            if file_size == 0:
                os.remove(cache_file)
                return None
            
            return {
                "cache_file": cache_file,
                "file_size": file_size,
                "created_time": datetime.fromtimestamp(os.path.getctime(cache_file)).isoformat()
            }
            
        except Exception as e:
            print(f"❌ 检查缓存失败: {e}")
            return None
    
    def _save_to_cache(self, text: str, audio_data: bytes) -> str:
        """保存音频数据到缓存"""
        try:
            cache_file = self._generate_text_path(text)
            # 保存音频文件
            with open(cache_file, 'wb') as f:
                f.write(audio_data)            
            print(f"音频已缓存: {os.path.basename(cache_file)}")
            return cache_file
            
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
            raise
      
    async def synthesize_async(self, text: str) -> Dict[str, Any]:
        """
        异步语音合成（使用ResultCallback）
        
        Args:
            text: 要合成的文本
        Returns:
            Dict[str, Any]: 合成结果
        """
        try:   
            cache_result = self._check_cache_exists(text)
            if cache_result:
                return {
                    "success": True,
                    "text": text,
                    "output_file": cache_result['cache_file'],
                    "file_size": cache_result['file_size'],
                    "from_cache": True
                }           

            # 创建结果收集器
            audio_data = bytearray()
            synthesis_complete = threading.Event()
            synthesis_error = None
            
            class TTSCallback(ResultCallback):
                def on_data(self, data):
                    """接收音频数据"""
                    if data:
                        audio_data.extend(data)
                
                def on_error(self, error):
                    """处理错误"""
                    nonlocal synthesis_error
                    synthesis_error = error
                    synthesis_complete.set()
                
                def on_complete(self):
                    """合成完成"""
                    synthesis_complete.set()
   

            # 创建回调对象
            callback = TTSCallback()
            
            # 创建语音合成器（传入回调）
            synthesizer = SpeechSynthesizer(
                model=self._cache_config['model'],
                voice=self._cache_config['voice'],
                callback=callback
            )
            
            # 在线程池中执行异步合成
            def run_synthesis():
                try:
                    synthesizer.call(text)
                except Exception as e:
                    nonlocal synthesis_error
                    synthesis_error = e
                    synthesis_complete.set()
            
            # 在新线程中运行合成
            synthesis_thread = threading.Thread(target=run_synthesis)
            synthesis_thread.start()
            
            # 异步等待合成完成（最多等待60秒）
            for _ in range(600):  # 60秒 * 10 = 600次检查
                if synthesis_complete.is_set():
                    break
                await asyncio.sleep(0.1)
            else:
                synthesis_error = "合成超时（60秒）"
                
            synthesis_thread.join(timeout=5)
            
            # 检查是否有错误
            if synthesis_error:
                raise Exception(f"语音合成失败: {synthesis_error}")
            
            # 检查音频数据
            if not audio_data:
                raise Exception("未接收到音频数据")
            
            cache_file = self._save_to_cache(text, bytes(audio_data))
            file_size = len(audio_data)

            # 验证文件是否真的保存成功
            if not os.path.exists(cache_file) or os.path.getsize(cache_file) == 0:
                raise Exception(f"音频文件保存失败或文件为空: {cache_file}")
            
            return {
                "success": True,
                "text": text,
                "output_file": cache_file,
                "file_size": file_size,
                "from_cache": False
            }
            
        except Exception as e:
            error_msg = f"语音合成失败: {str(e)}"        
            return {
                "success": False,
                "error": error_msg,
                "text": text,
                "timestamp": datetime.now().isoformat()
            }
    