# WbServer 功能说明

## 概述

WbServer（原 VirtualHostServer）是一个基于 WebSocket 的服务器组件，用于与客户端进行实时通信。主要功能包括接收客户端的视频播放时间戳，以及向客户端发送各种控制命令。

## 主要变更

### 1. 类名重命名
- **原名称**: `VirtualHostServer`
- **新名称**: `WbServer`
- **文件位置**: `src/plugin/wb_server_plugin.py`

### 2. 集成到浏览器初始化流程
- 在点击"初始化浏览器"按钮时，同时启动 WbServer
- 在关闭浏览器时，同时停止 WbServer
- 实现了浏览器和 WbServer 的生命周期同步

### 3. 首页面板增强
在首页面板新增了完整的 WbServer 管理界面：

#### 状态指示区域
- **服务状态**: 显示 WbServer 的当前状态（未启动/运行中/已停止）
- **连接数**: 显示当前连接的客户端数量
- **端口**: 显示 WbServer 监听的端口号

#### 控制按钮区域
提供 6 个控制按钮，可向所有连接的客户端发送命令：
- **Start**: 发送开始命令
- **Pause**: 发送暂停命令  
- **Goon**: 发送继续命令
- **XM**: 发送 XM 命令
- **Help**: 发送帮助命令（包含可用命令列表）
- **Switch**: 发送切换命令

#### 消息历史区域
- 实时显示接收和发送的消息历史
- 包含时间、方向、类型、客户端和内容信息
- 支持自动滚动到最新消息
- 提供"清空历史"功能

## 新增功能

### 1. 消息历史记录
```python
# 添加消息到历史
server.add_to_history("time_update", "received", data, client_id)

# 获取消息历史
history = server.get_message_history()

# 清空消息历史
server.clear_message_history()
```

### 2. 服务器状态管理
```python
# 获取服务器状态
status = server.get_server_status()
# 返回: {
#     "is_running": True/False,
#     "port": 8765,
#     "connected_clients": 2,
#     "current_time": 120.5,
#     "is_playing": True
# }

# 停止服务器
await server.stop_server()
```

### 3. 控制命令发送
```python
# 发送各种控制命令
await server.send_start_command()
await server.send_pause_command()
await server.send_goon_command()
await server.send_xm_command({"param": "value"})
await server.send_help_command()
await server.send_switch_command({"target": "stream2"})
```

## 接收功能

WbServer 可以接收客户端发送的以下消息：

### 1. 视频播放时间戳
```json
{
    "type": "time_update",
    "currentTime": 120.5
}
```

### 2. 播放状态
```json
{
    "type": "play_status", 
    "isPlaying": true
}
```

## 发送功能

WbServer 可以向客户端发送以下控制消息：

### 1. 控制命令格式
```json
{
    "type": "control",
    "command": "start|pause|goon|xm|help|switch",
    "timestamp": "2025-06-30T20:49:58.740Z",
    "additional_data": "可选的额外数据"
}
```

### 2. 帮助命令响应
```json
{
    "type": "control",
    "command": "help",
    "available_commands": ["start", "pause", "goon", "xm", "help", "switch"],
    "description": "可用的控制命令列表",
    "timestamp": "2025-06-30T20:49:58.740Z"
}
```

## 使用方法

### 1. 启动 WbServer
1. 输入直播地址
2. 点击"初始化浏览器"按钮
3. WbServer 会自动启动并在界面上显示状态

### 2. 发送控制命令
1. 确保 WbServer 状态显示为"运行中"
2. 点击对应的控制按钮（Start、Pause、Goon 等）
3. 命令会广播给所有连接的客户端
4. 在消息历史中可以看到发送记录

### 3. 监控连接状态
- 在状态指示区域实时查看连接数和服务状态
- 在消息历史区域查看所有通信记录
- 使用"清空历史"按钮清理消息记录

### 4. 关闭 WbServer
1. 点击"关闭浏览器"按钮
2. WbServer 会自动停止
3. 所有客户端连接会被正常关闭

## 技术实现

### 1. 异步架构
- 基于 `asyncio` 和 `websockets` 实现
- 支持并发处理多个客户端连接
- 非阻塞的消息处理机制

### 2. 线程安全
- GUI 操作在主线程执行
- WebSocket 服务器在独立线程运行
- 使用 `root.after()` 进行线程间通信

### 3. 自动端口管理
- 自动检测端口占用情况
- 智能选择可用端口（默认从 8765 开始）
- 端口冲突时自动寻找替代端口

## 错误处理

### 1. 连接错误
- 自动处理客户端断开连接
- 记录连接异常信息
- 维护连接数量的准确性

### 2. 服务器错误
- 端口占用时显示错误提示
- 启动失败时提供解决方案
- 异常情况下的资源清理

### 3. GUI 错误
- 命令发送失败时显示警告
- 服务器状态异常时的提示
- 用户操作错误的友好提示

## 配置选项

### 1. 服务器配置
- **默认端口**: 8765
- **主机地址**: localhost
- **连接超时**: 10 秒
- **Ping 间隔**: 20 秒

### 2. 消息历史配置
- **最大记录数**: 100 条
- **更新频率**: 每秒更新一次
- **显示格式**: 时间-方向-类型-客户端-内容

## 扩展性

WbServer 设计具有良好的扩展性：

1. **新控制命令**: 可以轻松添加新的控制命令类型
2. **消息格式**: 支持 JSON 格式的复杂消息结构
3. **客户端协议**: 兼容标准 WebSocket 协议
4. **状态管理**: 可扩展更多的状态信息

## 注意事项

1. **网络连接**: 确保防火墙允许 WebSocket 连接
2. **端口占用**: 避免其他程序占用默认端口 8765
3. **客户端兼容**: 客户端需要实现对应的消息处理逻辑
4. **资源清理**: 关闭应用时会自动清理所有连接和资源 