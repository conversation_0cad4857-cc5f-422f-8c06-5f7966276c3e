#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放控制器模块 - 使用pygame实现音频播放控制
支持播放、停止、状态检查等功能
"""
import os
import threading
import time
from typing import Optional, Callable
import pygame


class AudioController:
    """音频播放控制器 - 基于pygame实现"""
    
    def __init__(self):
        """初始化音频控制器"""
        self._initialized = False
        self._current_file = None
        self._is_playing = False
        self._play_complete_callback = None
        self._status_check_thread = None
        self._stop_checking = False
        self._init_pygame()
    
    def _init_pygame(self):
        """初始化pygame mixer"""
        try:
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=1024)
            pygame.mixer.init()
            self._initialized = True
            print("✅ 音频播放器初始化成功")
        except Exception as e:
            print(f"❌ 音频播放器初始化失败: {e}")
            self._initialized = False
    
    def play(self, audio_file_path: str, completion_callback: Optional[Callable] = None) -> bool:
        """
        播放音频文件
        
        Args:
            audio_file_path: 音频文件路径
            completion_callback: 播放完成回调函数
            
        Returns:
            bool: 是否成功开始播放
        """
        if not self._initialized:
            print("❌ 音频播放器未初始化")
            return False
        
        if not os.path.exists(audio_file_path):
            print(f"❌ 音频文件不存在: {audio_file_path}")
            return False
        
        try:
            # 停止当前播放
            self.stop()
            
            # 加载并播放新文件
            pygame.mixer.music.load(audio_file_path)
            pygame.mixer.music.play()
            
            self._current_file = audio_file_path
            self._is_playing = True
            self._play_complete_callback = completion_callback
            
            # 启动状态检查线程
            self._start_status_monitoring()
            
            print(f"🎵 开始播放: {os.path.basename(audio_file_path)}")
            return True
            
        except Exception as e:
            print(f"❌ 播放音频失败: {e}")
            self._is_playing = False
            return False
    
    def stop(self):
        """停止播放音频"""
        try:
            if self._is_playing:
                pygame.mixer.music.stop()
                self._is_playing = False
                self._current_file = None
                self._stop_status_monitoring()
                print("⏹️ 音频播放已停止")
        except Exception as e:
            print(f"❌ 停止播放失败: {e}")
    
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        if not self._initialized or not self._is_playing:
            return False
        
        try:
            return pygame.mixer.music.get_busy()
        except Exception:
            return False
    
    def get_current_file(self) -> Optional[str]:
        """获取当前播放的文件路径"""
        return self._current_file if self._is_playing else None
    
    def _start_status_monitoring(self):
        """启动播放状态监控线程"""
        self._stop_checking = False
        if self._status_check_thread and self._status_check_thread.is_alive():
            self._stop_status_monitoring()
        
        self._status_check_thread = threading.Thread(target=self._monitor_playback_status, daemon=True)
        self._status_check_thread.start()
    
    def _stop_status_monitoring(self):
        """停止播放状态监控"""
        self._stop_checking = True
        if self._status_check_thread and self._status_check_thread.is_alive():
            self._status_check_thread.join(timeout=1.0)
    
    def _monitor_playback_status(self):
        """监控播放状态的后台线程"""
        while not self._stop_checking and self._is_playing:
            try:
                if not pygame.mixer.music.get_busy():
                    # 播放完成
                    self._is_playing = False
                    self._current_file = None
                    
                    # 调用完成回调
                    if self._play_complete_callback:
                        try:
                            self._play_complete_callback()
                        except Exception as e:
                            print(f"❌ 播放完成回调执行失败: {e}")
                    
                    print("✅ 音频播放完成")
                    break
                
                time.sleep(0.1)  # 100ms检查一次
                
            except Exception as e:
                print(f"❌ 播放状态监控异常: {e}")
                break
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop()
            self._stop_status_monitoring()
            if self._initialized:
                pygame.mixer.quit()
                self._initialized = False
                print("✅ 音频播放器已清理")
        except Exception as e:
            print(f"❌ 音频播放器清理失败: {e}")
    
    def __del__(self):
        """析构函数 - 确保资源清理"""
        self.cleanup() 