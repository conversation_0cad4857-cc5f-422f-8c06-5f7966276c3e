# 主播稿键值冲突修复说明

## 问题描述

用户反馈在点击"生成主播稿"按钮后，生成的数据中key值存在错误。具体表现为：

```json
"0": {
  "txt": "今天开播就一个目的——把科三路考的通关干货给你们掰开揉碎讲透！...",
  "is_adv": "1",
  "source": "推广文案优化"
}
```

这里的问题是：
- 键值 `"0"` 应该专门用于开场白（`is_adv": "0"`）
- 但推广文案（`is_adv": "1"`）也在使用键值 `"0"`，造成冲突

## 问题分析

### 原因分析

在 `src/page/txt/zhubo.py` 的 `_generate_zhubo_data` 方法中：

1. **开场白**使用固定键值 `'0'`
2. **退场白**使用固定键值 `'2000'`
3. **推广文案**使用原始数据中的时间戳作为键值

当推广文案的原始时间戳恰好是 `"0"` 或 `"2000"` 时，就会与开场白或退场白的键值发生冲突。

### 冲突场景

```json
// 推广文案数据中包含冲突的时间戳
"adv_data": [
  {
    "list": {
      "0": {        // 与开场白键值冲突
        "txt": "推广内容",
        "is_adv": "1"
      },
      "2000": {     // 与退场白键值冲突
        "txt": "推广内容",
        "is_adv": "1"
      }
    }
  }
]
```

## 解决方案

### 1. 添加保留键检查机制

在处理推广文案时，检查时间戳是否与保留键冲突：

```python
reserved_keys = {'0', '2000'}  # 保留键：0用于开场白，2000用于退场白

if timestamp in reserved_keys:
    # 重新分配时间戳
    timestamp_float = float(timestamp)
    if timestamp_float == 0:
        new_timestamp = "0.1"      # 0 -> 0.1
    elif timestamp_float == 2000:
        new_timestamp = "1999.9"   # 2000 -> 1999.9
```

### 2. 添加来源信息标识

为所有内容添加 `source` 字段，便于区分内容来源：

- 开场白：`"source": "开场白"`
- 退场白：`"source": "退场白"`
- AI优化内容：`"source": "AI优化内容"`
- 推广文案：`"source": "推广文案优化"`

### 3. 修复后的数据结构

```json
[
  {
    "list": {
      "0": {                    // 开场白专用键值
        "txt": "欢迎大家来到直播间...",
        "is_adv": "0",
        "source": "开场白"
      }
    }
  },
  {
    "list": {
      "0.1": {                 // 原本冲突的推广文案，键值已修复
        "txt": "今天开播就一个目的...",
        "is_adv": "1",
        "source": "推广文案优化"
      }
    }
  },
  {
    "list": {
      "2000": {                // 退场白专用键值
        "txt": "感谢大家的观看...",
        "is_adv": "0",
        "source": "退场白"
      }
    }
  }
]
```

## 修复内容

### 文件修改

**文件**: `src/page/txt/zhubo.py`

**主要修改**:

1. **添加保留键检查**（第235-265行）
2. **添加来源信息**（第214、296-323、337行）
3. **键值冲突修复逻辑**（第245-265行）

### 测试验证

创建了测试脚本 `test_key_conflict_fix.py` 验证修复效果：

- ✅ 键值冲突检测和修复
- ✅ 特殊键值正确使用验证
- ✅ 数据结构完整性检查

## 修复效果

### 修复前
- 键值 `"0"` 可能被推广文案占用
- 键值 `"2000"` 可能被推广文案占用
- 缺少内容来源标识

### 修复后
- ✅ 键值 `"0"` 专门用于开场白
- ✅ 键值 `"2000"` 专门用于退场白
- ✅ 冲突的推广文案自动分配新键值（0→0.1, 2000→1999.9）
- ✅ 所有内容都有明确的来源标识
- ✅ 保持时间戳的相对顺序不变

## 使用说明

修复后，用户在使用"生成主播稿"功能时：

1. 系统会自动检测并修复键值冲突
2. 在控制台输出冲突修复日志
3. 生成的主播稿数据结构更加规范和清晰
4. 每个内容项都有明确的来源标识

这个修复确保了主播稿数据的键值唯一性和数据结构的一致性。
