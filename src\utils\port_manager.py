﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端口管理工具模块
提供通用的端口管理和系统操作工具函数
"""

import subprocess
import platform
import socket


def check_and_kill_port_process(port=8765):
    try:
        system = platform.system().lower()
        if system == 'windows':
            return _kill_port_process_windows(port)
        else:
            return _kill_port_process_unix(port)
            
    except Exception as e:
        print(f"❌ 检查端口占用时出错: {e}")
        return True


def _kill_port_process_windows(port):
    """Windows 系统的端口进程清理"""
    try:
        # Windows 系统使用 netstat 查找占用端口的进程
        cmd = f'netstat -ano | findstr :{port}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            killed_any = False
            
            for line in lines:
                if f':{port}' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        try:
                            pid = int(pid)
                            print(f"🔍 发现进程 PID {pid} 占用端口 {port}")
                            
                            # 使用 taskkill 强制终止进程
                            kill_cmd = f'taskkill /PID {pid} /F'
                            kill_result = subprocess.run(kill_cmd, shell=True, capture_output=True, text=True)
                            
                            if kill_result.returncode == 0:
                                print(f"✅ 成功终止进程 PID {pid}")
                                killed_any = True
                            else:
                                print(f"⚠️ 终止进程 PID {pid} 失败: {kill_result.stderr}")
                        except ValueError:
                            continue
                        except Exception as e:
                            print(f"⚠️ 处理进程 PID {pid} 时出错: {e}")
            
            if killed_any:
                print(f"✅ 端口 {port} 清理完成")
            return True
            
    except Exception as e:
        print(f"❌ Windows 端口检查失败: {e}")
        return False


def _kill_port_process_unix(port):
    """Linux/macOS 系统的端口进程清理"""
    try:
        # Linux/macOS 系统使用 lsof 和 kill
        cmd = f'lsof -ti :{port}'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            killed_any = False
            
            for pid in pids:
                try:
                    pid = int(pid.strip())
                    print(f"🔍 发现进程 PID {pid} 占用端口 {port}")
                    
                    # 使用 kill 终止进程
                    kill_result = subprocess.run(['kill', '-9', str(pid)], capture_output=True, text=True)
                    
                    if kill_result.returncode == 0:
                        print(f" 成功终止进程 PID {pid}")
                        killed_any = True
                    else:
                        print(f" 终止进程 PID {pid} 失败: {kill_result.stderr}")
                except ValueError:
                    continue
                except Exception as e:
                    print(f" 处理进程 PID {pid} 时出错: {e}")
            
            if killed_any:
                print(f" 端口 {port} 清理完成")
            return True
        else:
            print(f" 端口 {port} 未被占用")
            return True
            
    except Exception as e:
        print(f" Unix 端口检查失败: {e}")
        return False


def is_port_available(host="localhost", port=8765):
    """
    检查指定端口是否可用
    
    Args:
        host (str): 主机地址，默认为 localhost
        port (int): 端口号，默认为 8765
        
    Returns:
        bool: 端口可用返回 True，否则返回 False
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            return result != 0  # 连接失败说明端口可用
    except Exception:
        return False


def find_available_port(host="localhost", start_port=8765, max_attempts=10):
    """
    寻找可用端口
    
    Args:
        host (str): 主机地址，默认为 localhost
        start_port (int): 起始端口号，默认为 8765
        max_attempts (int): 最大尝试次数，默认为 10
        
    Returns:
        int or None: 可用端口号，如果没找到返回 None
    """
    for port in range(start_port, start_port + max_attempts):
        if is_port_available(host, port):
            return port
    return None


def get_system_info():
    """
    获取系统信息
    
    Returns:
        dict: 包含系统信息的字典
    """
    return {
        "system": platform.system(),
        "platform": platform.platform(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version()
    }


def safe_kill_process(pid, force=False):
    """
    安全地终止进程
    
    Args:
        pid (int): 进程ID
        force (bool): 是否强制终止，默认为 False
        
    Returns:
        bool: 成功返回 True，否则返回 False
    """
    try:
        system = platform.system().lower()
        
        if system == 'windows':
            cmd = f'taskkill /PID {pid} {"" if not force else "/F"}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:
            signal = '-9' if force else '-15'
            result = subprocess.run(['kill', signal, str(pid)], capture_output=True, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f" 终止进程 {pid} 失败: {e}")
        return False


if __name__ == "__main__":
    # 测试功能
    print(" 测试端口工具函数...")
    print(f"系统信息: {get_system_info()}")
    print(f"端口 8765 可用性: {is_port_available('localhost', 8765)}")
    print(f"寻找可用端口: {find_available_port('localhost', 8765)}")
    check_and_kill_port_process(8765)
