#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音文件生成处理器模块 - 处理主播稿数据的语音文件生成
从主播稿数据中提取文本内容，生成对应的语音文件和zhibo缓存
"""
import os
import json
import hashlib
import threading
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.utils.tts_hash_base import TTSHashBase


class VoiceProcessor(TTSHashBase):
    """语音文件生成处理器"""
    
    def __init__(self, main_controller):
        # 调用基类初始化方法
        super().__init__()
        self.main_controller = main_controller
        self.zhibo_cache_dir = main_controller.zhibo_cache_dir
        self.tts_plugin = main_controller.tts_plugin
        
        # 确保zhibo缓存目录存在（voice缓存目录由基类管理）
        if not os.path.exists(self.zhibo_cache_dir):
            os.makedirs(self.zhibo_cache_dir)
            print(f"✅ 创建缓存目录: {self.zhibo_cache_dir}")
    
    def generate_zhibo_voice_data(self, url: str = None):
        """生成直播语音数据并保存到zhibo缓存 - 优化版：使用MD5即时编码"""
        try:
            print("🎵 开始生成直播语音数据...")

            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            print(f"🔑 生成的缓存键: {cache_key}")

            # 获取主播稿数据文件路径
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            print(f"📁 主播稿文件路径: {zhubo_file_path}")

            if not os.path.exists(zhubo_file_path):
                print(f"⚠️ 主播稿数据文件不存在: {zhubo_file_path}")
                print("   请先在文案页面生成主播稿数据")
                return

            # 读取主播稿数据
            zhubo_data = self._load_zhubo_data(zhubo_file_path)
            if not zhubo_data:
                print("❌ 主播稿数据为空或格式错误")
                return

            print(f"📖 成功读取主播稿数据，包含 {len(zhubo_data)} 个数据项")

            # 提取所有文本内容
            all_texts = self._extract_texts_from_zhubo_data(zhubo_data)
            print(f"📝 从主播稿数据中提取到 {len(all_texts)} 条文本")

            if len(all_texts) == 0:
                print("❌ 提取的文本内容为空，检查主播稿数据格式")
                return

            # 打印前几条文本用于调试
            for i, (timestamp, text) in enumerate(list(all_texts.items())[:3]):
                print(f"   📄 文本 {i+1}: {timestamp} -> {text[:50]}...")

            # 检查语音文件是否存在（使用MD5即时编码）
            missing_files = self._check_missing_voice_files(all_texts)
            missing_count = len(missing_files)

            if missing_count > 0:
                print(f"⚠️ 检测到 {missing_count} 个语音文件缺失，需要进行语音转换")
                # 打印缺失的文件信息
                for i, (timestamp, text) in enumerate(list(missing_files.items())[:3]):
                    print(f"   📄 缺失 {i+1}: {timestamp} -> {text[:30]}...")
            else:
                print("✅ 所有语音文件都已存在")

            # 保存语音缓存信息（不再保存voice_data映射）
            self._save_zhibo_voice_cache_optimized(cache_key, all_texts, missing_count)

            print(f"✅ 主播稿语音文件处理完成")

        except Exception as e:
            print(f"❌ 生成直播语音数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _get_zhubo_file_path(self, cache_key: str) -> str:
        """获取主播稿文件路径"""
        zhubo_filename = f"{cache_key}.json"
        return os.path.join(self.zhibo_cache_dir, zhubo_filename)
    
    def _load_zhubo_data(self, file_path: str) -> Optional[List[Dict]]:
        """加载主播稿数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                print(f"❌ 主播稿数据格式错误：期望数组，得到 {type(data)}")
                return None
            
            return data
        except Exception as e:
            print(f"❌ 加载主播稿数据失败: {e}")
            return None
    
    def _extract_texts_from_zhubo_data(self, zhubo_data: List[Dict]) -> Dict[str, str]:
        """从主播稿数据中提取所有文本内容 - 修复版：正确处理数据格式

        Args:
            zhubo_data: 主播稿数据数组

        Returns:
            Dict[时间戳, 文本内容]
        """
        all_texts = {}

        try:
            print("🔍 开始提取文本，使用修复后的数据格式适配逻辑")

            # 打印数据结构信息用于调试
            print(f"📊 主播稿数据: {len(zhubo_data)} 个项目组")

            for item_index, item in enumerate(zhubo_data):
                if not isinstance(item, dict) or 'list' not in item:
                    continue

                item_list = item['list']
                if not isinstance(item_list, dict):
                    continue

                # 提取时间戳和文本
                for timestamp, content in item_list.items():
                    # 处理不同的内容格式
                    text_content = None

                    # 1. 处理字符串格式
                    if isinstance(content, str) and content.strip():
                        text_content = content.strip()
                        print(f"📝 提取文本 {len(all_texts)+1} (字符串): {text_content[:50]}...")

                    # 2. 处理字典格式
                    elif isinstance(content, dict):
                        # 2.1 处理包含txt字段的字典
                        if 'txt' in content:
                            txt_value = content['txt']
                            if isinstance(txt_value, str) and txt_value.strip():
                                text_content = txt_value.strip()
                                print(f"📝 提取文本 {len(all_texts)+1} (普通): {text_content[:50]}...")

                        # 2.2 处理推广文案格式
                        elif 'content' in content and isinstance(content['content'], str):
                            text_content = content['content'].strip()
                            print(f"📝 提取文本 {len(all_texts)+1} (推广): {text_content[:50]}...")

                    # 只保存有效的文本内容
                    if text_content:
                        all_texts[str(timestamp)] = text_content

            print(f"📝 提取到 {len(all_texts)} 条zhibo文案")

            # 打印前几条提取的文本用于调试
            if all_texts:
                print("📋 提取的文本示例:")
                for i, (timestamp, text) in enumerate(list(all_texts.items())[:3]):
                    print(f"   {i+1}. {timestamp}: {text[:50]}...")

            return all_texts

        except Exception as e:
            print(f"❌ 提取文本内容失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def _is_numeric_timestamp(self, timestamp: str) -> bool:
        """检查时间戳是否为数字格式"""
        try:
            float(timestamp)
            return True
        except ValueError:
            return False
    
    def _check_missing_voice_files(self, texts: Dict[str, str]) -> Dict[str, str]:
        """检查缺失的语音文件（使用MD5即时编码）

        Args:
            texts: {时间戳: 文本内容}

        Returns:
            Dict[时间戳, 文本内容] - 只包含缺失文件的文本
        """
        missing_files = {}
        total_files = len(texts)
        existing_files = 0

        for timestamp, text in texts.items():
            try:
                # 使用MD5即时编码生成文件路径
                voice_file_path = self._generate_text_path(text)

                # 检查文件是否存在
                if not os.path.exists(voice_file_path):
                    missing_files[timestamp] = text
                else:
                    existing_files += 1
            except Exception as e:
                print(f"❌ 检查语音文件失败 (时间戳: {timestamp}): {e}")
                missing_files[timestamp] = text

        # 打印统计信息
        print(f"📊 语音文件统计：总计 {total_files} 条，已存在 {existing_files} 条，缺失 {len(missing_files)} 条")
        return missing_files

    def _save_zhibo_voice_cache_optimized(self, cache_key: str, all_texts: Dict[str, str], missing_count: int):
        """保存优化版语音缓存信息（不再保存voice_data映射）

        Args:
            cache_key: 缓存键
            all_texts: {时间戳: 文本内容}
            missing_count: 缺失文件数量
        """
        try:
            # 构建保存数据结构
            zhibo_voice_data = {
                "timestamp": datetime.now().isoformat(),
                "cache_key": cache_key,
                "total_count": len(all_texts),
                "missing_count": missing_count,
                "md5_encoding": True,  # 标记使用MD5即时编码
                "original_texts": True  # 标记原始文本已保存在主播稿文件中
            }

            # 保存到zhibo语音缓存文件
            voice_cache_filename = f"voice_{cache_key}.json"
            voice_cache_path = os.path.join(self.zhibo_cache_dir, voice_cache_filename)

            print(f"💾 准备保存优化版语音缓存: {voice_cache_filename}")
            print(f"   数据条目数: {len(all_texts)}")

            with open(voice_cache_path, 'w', encoding='utf-8') as f:
                json.dump(zhibo_voice_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 优化版语音缓存保存成功: {voice_cache_path}")

        except Exception as e:
            print(f"❌ 保存语音缓存失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_voice_cache_info(self, cache_key: str) -> Optional[Dict]:
        """获取语音缓存信息"""
        try:
            voice_cache_filename = f"voice_{cache_key}.json"
            voice_cache_path = os.path.join(self.zhibo_cache_dir, voice_cache_filename)
            
            if not os.path.exists(voice_cache_path):
                return None
            
            with open(voice_cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"❌ 获取语音缓存信息失败: {e}")
            return None
    
    def check_voice_files_exist_by_texts(self, texts: Dict[str, str]) -> Dict[str, bool]:
        """检查语音文件是否存在（使用MD5即时编码）

        Args:
            texts: {时间戳: 文本内容}

        Returns:
            Dict[时间戳, 是否存在]
        """
        existence_check = {}

        for timestamp, text in texts.items():
            try:
                # 使用MD5即时编码生成文件路径
                voice_path = self._generate_text_path(text)
                existence_check[timestamp] = os.path.exists(voice_path)
            except Exception as e:
                print(f"❌ 检查语音文件存在性失败 (时间戳: {timestamp}): {e}")
                existence_check[timestamp] = False

        return existence_check

    def check_voice_files_exist(self, voice_data: Dict[str, str]) -> Dict[str, bool]:
        """检查语音文件是否存在（兼容旧版本）

        Args:
            voice_data: {时间戳: 语音文件路径}

        Returns:
            Dict[时间戳, 是否存在]
        """
        existence_check = {}

        for timestamp, voice_path in voice_data.items():
            try:
                existence_check[timestamp] = os.path.exists(voice_path)
            except Exception as e:
                print(f"❌ 检查语音文件存在性失败 (时间戳: {timestamp}): {e}")
                existence_check[timestamp] = False

        return existence_check
    
    async def generate_missing_voice_files_async(self, all_texts: Dict[str, str] = None,
                                               progress_callback: Optional[callable] = None,
                                               completion_callback: Optional[callable] = None):
        """异步生成缺失语音文件的实现 - 优化版：使用MD5即时编码"""
        try:
            # 如果没有传入文本数据，从主播稿文件中提取
            if all_texts is None:
                cache_key = self.main_controller._get_ai_cache_key()
                zhubo_file_path = self._get_zhubo_file_path(cache_key)

                if not os.path.exists(zhubo_file_path):
                    raise Exception(f"主播稿数据文件不存在: {zhubo_file_path}")

                # 读取主播稿数据获取原始文本
                zhubo_data = self._load_zhubo_data(zhubo_file_path)
                if not zhubo_data:
                    raise Exception("主播稿数据为空或格式错误")

                # 提取文本内容
                all_texts = self._extract_texts_from_zhubo_data(zhubo_data)

            # 检查哪些文件缺失（使用MD5即时编码）
            missing_texts = self._check_missing_voice_files(all_texts)

            if not missing_texts:
                if completion_callback:
                    completion_callback({
                        "success": True,
                        "message": "所有语音文件都已存在",
                        "total_count": len(all_texts),
                        "missing_count": 0
                    })
                return

            total_missing = len(missing_texts)
            success_count = 0
            error_count = 0

            print(f"🎵 开始生成 {total_missing} 个缺失的语音文件...")

            for i, (timestamp, text_content) in enumerate(missing_texts.items(), 1):
                try:
                    print(f"🔄 生成语音 {i}/{total_missing}: {timestamp} -> {text_content[:30]}...")

                    # 调用TTS插件生成语音
                    result = await self.tts_plugin.synthesize_async(text_content)
                    if result.get("success"):
                        success_count += 1
                        print(f"✅ 语音生成成功: {timestamp}")
                    else:
                        error_count += 1
                        print(f"❌ 语音文件生成失败 ({timestamp}): {result.get('error', '未知错误')}")

                except Exception as e:
                    error_count += 1
                    print(f"❌ 处理语音文件失败 ({timestamp}): {e}")
            
            # 返回结果
            if completion_callback:
                completion_callback({
                    "success": True,
                    "total_count": len(voice_data),
                    "missing_count": total_missing,
                    "success_count": success_count,
                    "error_count": error_count,
                    "message": f"语音文件生成完成：成功 {success_count} 个，失败 {error_count} 个"
                })
        
        except Exception as e:
            print(f"❌ 生成缺失语音文件失败: {e}")
            if completion_callback:
                completion_callback({
                    "success": False,
                    "error": str(e)
                }) 