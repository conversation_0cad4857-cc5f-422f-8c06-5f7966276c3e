# 模块拆分说明

## 概述

按照功能模块将原来的 `integrated_launcher.py` 拆分为多个页面模块，提高代码的可维护性和可扩展性。

## 拆分后的目录结构

```
src/
├── integrated_launcher.py        # 主入口文件
├── page/                         # 页面模块目录
│   ├── __init__.py              # 页面模块初始化
│   ├── index.py                 # 首页模块（直播控制）
│   ├── txt.py                   # 文案模块（AI优化、TTS转换）
│   └── setting.py               # 配置模块（系统设置）
└── plugin/                       # 插件目录（保持不变）
    ├── browser_plugin.py
    └── tts_plugin.py
```

## 各模块职责

### 1. `integrated_launcher.py` - 主入口

**保留功能：**
- 主窗口框架和标签页管理
- 缓存目录管理和数据共享
- 插件初始化（AI优化器、TTS插件）
- 缓存管理相关方法
- 窗口居中和关闭事件处理

**共享状态管理：**
- `self.cached_data` - 原始数据缓存
- `self.ai_optimized_data` - AI优化结果缓存
- `self.edited_data` - 编辑内容缓存
- `self.tts_results_data` - TTS结果缓存
- `self.current_display_mode` - 当前显示模式

### 2. `page/index.py` - 首页模块

**功能职责：**
- 浏览器初始化和管理
- 直播控制（开始/停止）
- 浏览器状态监控
- 首页UI布局

**主要方法：**
- `setup_home_tab()` - 创建首页界面
- `initialize_browser()` - 初始化浏览器
- `toggle_stream_status()` - 切换直播状态
- `_click_start_button_async()` - 异步点击开始按钮

### 3. `page/txt.py` - 文案模块

**功能职责：**
- 内容数据获取和显示
- AI文案优化
- TTS语音转换
- 内容编辑和自动保存
- TTS结果查看器

**主要方法：**
- `setup_content_tab()` - 创建文案界面
- `start_ai_optimization()` - AI文案优化
- `start_tts_conversion()` - TTS语音转换
- `display_cached_data()` - 显示缓存数据

### 4. `page/setting.py` - 配置模块

**功能职责：**
- 系统配置管理
- 参数设置界面
- 配置文件管理（后续扩展）

**主要方法：**
- `setup_config_tab()` - 创建配置界面

## 模块间通信

### 共享状态访问
各页面模块通过 `self.main_controller` 访问主控制器的共享状态和方法：

```python
# 访问缓存数据
self.main_controller.cached_data[url]


# 访问插件实例
self.main_controller.ai_optimizer
self.main_controller.tts_plugin
```

### 状态同步
- 各模块的状态变化会自动同步到主控制器
- 标签页切换时会触发对应模块的刷新

## 路径调整

### 插件导入路径
```python
# 原来：from .plugin import xxx
# 现在：通过主控制器访问插件实例
self.main_controller.ai_optimizer
```

### 缓存路径
缓存路径统一由主控制器管理，各模块通过主控制器访问：
```python
self.main_controller.cache_dir
self.main_controller.ai_cache_dir
self.main_controller.voice_cache_dir
```

## 优势

1. **代码可维护性提升**
   - 各功能模块职责清晰
   - 代码结构更加清晰
   - 便于单独调试和测试

2. **可扩展性增强**
   - 新功能可以独立成模块
   - 模块间依赖关系明确
   - 便于团队协作开发

3. **代码复用性**
   - 各模块可以独立复用
   - 公共功能统一管理
   - 减少代码重复

## 兼容性

- 保持原有功能不变
- 用户界面保持一致
- 所有缓存数据向后兼容
- 插件接口保持不变

## 运行方式

保持原有运行方式不变：
```bash
python -m src.integrated_launcher
```

或者：
```bash
python run.py
``` 