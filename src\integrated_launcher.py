#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合版直播启动器 - 标签页界面（重构后的主入口）
包含首页(直播控制)、文案管理、配置三个标签
"""
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import asyncio
import sys
import os
import json
import hashlib
from datetime import datetime
from urllib.parse import urlparse, parse_qs
import configparser

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 插件导入
try:
    from .plugin import iPhone14LandscapeBrowserPlugin, DeepSeekPlugin, DoubaoPlugin, TTSPlugin
except ImportError as e:
    print(f"❌ 关键插件导入失败: {e}")
    print("   请确保从项目根目录运行, 例如: python -m src.integrated_launcher")
    iPhone14LandscapeBrowserPlugin = None
    DeepSeekPlugin = None
    DoubaoPlugin = None
    TTSPlugin = None

# 导入页面模块
try:
    from .page import IndexPage, TxtPage, SettingPage
except ImportError as e:
    print(f"❌ 页面模块导入失败: {e}")
    IndexPage = None
    TxtPage = None
    SettingPage = None

class IntegratedLiveStreamLauncher:
    """整合版直播启动器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("直播控制台 - 集成版")
        self.root.geometry("1000x700")
        
        # 直播相关状态
        self.is_running = False
        self.is_initialized = False
        self.stream_status = False
        self.browser = None
        
        # 添加插件类的引用
        self.iPhone14LandscapeBrowserPlugin = iPhone14LandscapeBrowserPlugin
        
        # 数据管理相关 - 使用绝对路径
        # 当前文件在 src/ 目录下，所以直接使用相对路径
        src_dir = os.path.dirname(os.path.abspath(__file__))
        self.cache_dir = os.path.join(src_dir, "cache")
        self.ai_cache_dir = os.path.join(src_dir, "cache", "ai_optimized")
        self.voice_cache_dir = os.path.join(src_dir, "cache", "voice")
        self.zhibo_cache_dir = os.path.join(src_dir, "cache", "zhibo")
        self.config_dir = os.path.join(src_dir, "config")
        self.cached_data = {}
        self.ai_optimized_data = {}
        self.adv_data = {}  # 推广文案数据缓存
        
        # 加载配置文件
        self.config = None
        self._load_config()
        

        
        # 编辑相关状态
        self.is_loading_content = False
        self.last_saved_content = ""
        self.auto_save_timer = None
        self.current_display_mode = "none"  # 当前显示模式: "original", "ai_optimized", "none"
        
        # 确保缓存目录存在
        self._ensure_cache_dir()
        
        # 初始化AI插件
        self.ai_optimizer = None
        self._init_ai_optimizer()
        # 初始化语音合成插件
        self.tts_plugin = None
        self._init_tts_plugin()
        
        # 初始化AI文案TTS处理器
        self.ai_tts_processor = None
        self._init_ai_tts_processor()
        
        # 初始化推广文案处理器
        self.adv_data_processor = None
        self._init_adv_data_processor()
        
        # 初始化主播稿处理器
        self.zhubo_processor = None
        self._init_zhubo_processor()
        
        # 初始化语音处理器
        self.voice_processor = None
        self._init_voice_processor()
        
        # 初始化页面模块
        self.index_page = None
        self.txt_page = None
        self.setting_page = None
        
        # 创建GUI变量（由各页面模块使用）
        self.content_url_var = tk.StringVar()
        self.content_url_var.trace_add('write', self.on_url_changed)
        
        # 创建GUI
        self.setup_gui()
    
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 居中显示窗口
        self.center_window()
        
        # 加载缓存数据
        self.load_cache()
        
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        for directory in [self.cache_dir, self.ai_cache_dir, 
                         self.voice_cache_dir, self.zhibo_cache_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✅ 创建缓存目录: {directory}")
    
    def _load_config(self):
        """加载配置文件"""
        try:
            config_file = os.path.join(self.config_dir, "config.ini")
            if not os.path.exists(config_file):
                print(f"❌ 配置文件不存在: {config_file}")
                return False
            
            self.config = configparser.ConfigParser()
            self.config.read(config_file, encoding='utf-8')
            
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return False
    
    def _init_ai_optimizer(self):
        """根据配置文件动态初始化AI插件"""
        try:
            plugin_type = self.config.get('DEFAULT', 'AI_Plugin', fallback='deepseek').lower()
            plugin_config = {}
            if plugin_type == 'doubao':
                plugin_config = {
                    "api_key": self.config.get('DEFAULT', 'doubao_key', fallback=''),
                    "model": self.config.get('DEFAULT', 'doubao_model', fallback='ep-20250703110930-f2mvd'),
                    "temperature": float(self.config.get('DEFAULT', 'temperature', fallback='1.2')),
                    "max_tokens": int(self.config.get('DEFAULT', 'max_tokens', fallback='48000'))
                }
                self.ai_optimizer = DoubaoPlugin(plugin_config=plugin_config)
    
            elif plugin_type == 'deepseek':
                plugin_config = {
                    "api_key": self.config.get('DEFAULT', 'deepseek_key', fallback=''),
                    "model": self.config.get('DEFAULT', 'deepseek_model', fallback='deepseek-reasoner'),
                    "temperature": float(self.config.get('DEFAULT', 'temperature', fallback='1.2')),
                    "max_tokens": int(self.config.get('DEFAULT', 'max_tokens', fallback='48000'))
                }
                self.ai_optimizer = DeepSeekPlugin(plugin_config=plugin_config)

            else:
                print(f"❌ 未找到AI插件: {plugin_type}")
                return False
            return True
            
        except Exception as e:
            print(f"❌ AI插件初始化失败: {e}")
            return False
    
    def _init_tts_plugin(self):
        """初始化语音合成插件"""
        try:
            from .plugin import TTSPlugin
            self.tts_plugin = TTSPlugin()
            return True
        except Exception as e:
            print(f"❌ 语音合成插件初始化失败: {e}")
            return False
    
    def _init_ai_tts_processor(self):
        """初始化AI文案TTS处理器"""
        try:
            from .page.txt.aitxt import AITxtTTSProcessor
            self.ai_tts_processor = AITxtTTSProcessor(self)
            return True
        except Exception as e:
            print(f"❌ AI文案TTS处理器初始化失败: {e}")
            return False
    
    def _init_adv_data_processor(self):
        """初始化推广文案处理器"""
        try:
            from .page.txt.adv import AdvDataProcessor
            self.adv_data_processor = AdvDataProcessor(self)
            return True
        except Exception as e:
            print(f"❌ 推广文案处理器初始化失败: {e}")
            return False
    
    def _init_zhubo_processor(self):
        """初始化主播稿处理器"""
        try:
            from .page.txt.zhubo import ZhuboDataProcessor
            self.zhubo_processor = ZhuboDataProcessor(self)
            return True
        except Exception as e:
            print(f"❌ 主播稿处理器初始化失败: {e}")
            return False
    
    def _init_voice_processor(self):
        """初始化语音处理器"""
        try:
            from .page.index.voice import VoiceProcessor
            self.voice_processor = VoiceProcessor(self)
            return True
        except Exception as e:
            print(f"❌ 语音处理器初始化失败: {e}")
            return False
    
    def _get_ai_cache_key(self):
        """生成AI缓存的唯一键值（基于首页直播地址URL中的index和lesson_id参数）
        
        Args:
            url: 文案内容URL（已废弃，仅为兼容性保留）
        
        Returns:
            str: 基于直播地址参数生成的缓存键
        """
        # 获取首页直播地址URL
        live_url = self.url_var.get().strip() if hasattr(self, 'url_var') else ""
        if not live_url:
            raise ValueError("首页直播地址为空，无法生成缓存键")
        
        # 解析直播地址URL获取参数
        parsed_url = urlparse(live_url)
        query_params = parse_qs(parsed_url.query)
        
        # 提取index和lesson_id参数
        index = query_params.get('index', [None])[0]
        lesson_id = query_params.get('lesson_id', [None])[0]
        
        if index is None or lesson_id is None:
            raise ValueError(f"直播地址缺少必需参数: index={index}, lesson_id={lesson_id}, URL: {live_url}")
        
        # 组合index和lesson_id生成唯一键
        combined_content = f"index{index}lesson_id{lesson_id}"
        cache_key = hashlib.md5(combined_content.encode('utf-8')).hexdigest()

        return cache_key
    
    def _get_ai_cache_file_path(self):
        """获取AI优化缓存文件路径（基于直播地址参数）
        
        Args:
            url: 文案内容URL（已废弃，仅为兼容性保留）
        
        Returns:
            str: AI缓存文件路径
        """
        cache_key = self._get_ai_cache_key()
        filename = f"ai_{cache_key}.json"
        return os.path.join(self.ai_cache_dir, filename)
    
    def _save_original_cache(self,content):
        """保存原始缓存数据到AI优化文件的original_data字段（基于直播地址参数）"""
        try:
            # 验证JSON格式
            import json
            from tkinter import messagebox
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                # 弹框提示JSON格式错误，不再继续
                messagebox.showerror("JSON格式错误", 
                    f"保存失败：内容不是有效的JSON格式\n\n"
                    f"错误位置：第{e.lineno}行 第{e.colno}列\n"
                    f"错误信息：{str(e)}")
                return
            
            # 生成基于直播地址参数的缓存键
            cache_key = self._get_ai_cache_key()
            
            # 创建缓存数据结构
            cache_data = {
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
            # 使用缓存键作为内存中的键值
            self.cached_data[cache_key] = cache_data
            
            # 更新或创建AI优化文件
            if cache_key in self.ai_optimized_data:
                # 更新现有AI优化数据
                ai_data = self.ai_optimized_data[cache_key].copy()
                ai_data["original_data"] = cache_data
                ai_data["timestamp"] = datetime.now().isoformat()
                
                # 保存到内存
                self.ai_optimized_data[cache_key] = ai_data
                
                # 保存到AI优化文件
                self._save_ai_cache(ai_data)
                
                print(f"✅ 更新AI优化文件中的原始数据: {cache_key}")
            else:
                # 创建新的AI优化文件模板
                ai_template = {
                    "original_data": cache_data,
                    "optimized_content": None,
                    "success": False,
                    "timestamp": datetime.now().isoformat(),
                    "cache_key": cache_key,
                    "cached_timestamp": datetime.now().isoformat()
                }
                
                # 保存到内存和文件
                self.ai_optimized_data[cache_key] = ai_template
                self._save_ai_cache(ai_template)
                
                print(f"✅ 创建新的AI优化文件模板并保存原始数据: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存原始缓存失败: {e}")
    
    def _save_ai_optimized_cache(self, content):
        """保存AI优化内容的编辑到optimized_content字段（基于直播地址参数）"""
        try:
            # 验证JSON格式
            import json
            from tkinter import messagebox
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                # 弹框提示JSON格式错误，不再继续
                messagebox.showerror("JSON格式错误", 
                    f"保存失败：内容不是有效的JSON格式\n\n"
                    f"错误位置：第{e.lineno}行 第{e.colno}列\n"
                    f"错误信息：{str(e)}")
                return
            
            # 生成基于直播地址参数的缓存键
            cache_key = self._get_ai_cache_key()
            
            # 检查是否存在AI优化数据
            if cache_key not in self.ai_optimized_data:
                messagebox.showerror("错误", "未找到AI优化数据，无法保存编辑内容")
                return
            
            # 更新AI优化数据中的optimized_content字段
            ai_data = self.ai_optimized_data[cache_key].copy()
            ai_data["optimized_content"] = data
            ai_data["timestamp"] = datetime.now().isoformat()
            
            # 重新计算MD5用于变化检测
            original_data = ai_data.get("original_data")
            content_md5 = self._calculate_content_md5(original_data, data) if hasattr(self, '_calculate_content_md5') else None
            if content_md5:
                ai_data["optimized_md5"] = content_md5
            
            # 保存到内存
            self.ai_optimized_data[cache_key] = ai_data
            
            # 保存到AI优化文件
            self._save_ai_cache( ai_data)
            
            print(f"✅ 保存AI优化内容编辑: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存AI优化缓存失败: {e}")
    
    def _save_adv_data_cache(self, url, content):
        """保存推广文案编辑到adv_data字段（基于直播地址参数）"""
        try:
            # 验证JSON格式
            import json
            from tkinter import messagebox
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                # 弹框提示JSON格式错误，不再继续
                messagebox.showerror("JSON格式错误", 
                    f"保存失败：内容不是有效的JSON格式\n\n"
                    f"错误位置：第{e.lineno}行 第{e.colno}列\n"
                    f"错误信息：{str(e)}")
                return
            
            # 生成基于直播地址参数的缓存键
            cache_key = self._get_ai_cache_key()
            
            # 检查是否存在AI优化数据
            if cache_key not in self.ai_optimized_data:
                messagebox.showerror("错误", "未找到AI优化数据，无法保存推广文案内容")
                return
            
            # 更新AI优化数据中的adv_data字段
            ai_data = self.ai_optimized_data[cache_key].copy()
            ai_data["adv_data"] = data
            ai_data["timestamp"] = datetime.now().isoformat()
            
            # 保存到内存
            self.ai_optimized_data[cache_key] = ai_data
            self.adv_data[cache_key] = data  # 同时保存到推广文案缓存
            
            # 保存到AI优化文件
            self._save_ai_cache( ai_data)
            
            print(f"✅ 保存推广文案编辑: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存推广文案缓存失败: {e}")
    
    def _save_adv_optimized_cache(self, content):
        """保存推广文案优化编辑到adv_data字段（基于直播地址参数）"""
        try:
            # 验证JSON格式
            import json
            from tkinter import messagebox
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                # 弹框提示JSON格式错误，不再继续
                messagebox.showerror("JSON格式错误", 
                    f"保存失败：内容不是有效的JSON格式\n\n"
                    f"错误位置：第{e.lineno}行 第{e.colno}列\n"
                    f"错误信息：{str(e)}")
                return
            
            # 生成基于直播地址参数的缓存键
            cache_key = self._get_ai_cache_key()
            
            # 检查是否存在AI优化数据
            if cache_key not in self.ai_optimized_data:
                messagebox.showerror("错误", "未找到AI优化数据，无法保存推广文案优化内容")
                return
            
            # 更新AI优化数据中的adv_data字段
            ai_data = self.ai_optimized_data[cache_key].copy()
            ai_data["adv_data"] = data
            ai_data["timestamp"] = datetime.now().isoformat()
            
            # 保存到内存
            self.ai_optimized_data[cache_key] = ai_data
            
            # 保存到AI优化文件
            self._save_ai_cache(ai_data)
            
            print(f"✅ 保存推广文案优化编辑: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存推广文案优化缓存失败: {e}")
    
    def _save_content_cache(self, content, field_name):
        """通用内容缓存保存方法"""
        try:
            # 内容为纯文本格式，不需要JSON验证
            if not isinstance(content, str):
                content = str(content)
            
            # 生成基于直播地址参数的缓存键
            cache_key = self._get_ai_cache_key()
            
            # 检查是否存在AI优化数据
            if cache_key not in self.ai_optimized_data:
                # 如果不存在，创建基本结构
                ai_data = {
                    "cache_key": cache_key,
                    "timestamp": datetime.now().isoformat(),
                    "cached_timestamp": datetime.now().isoformat()
                }
            else:
                ai_data = self.ai_optimized_data[cache_key].copy()
            
            # 更新AI优化数据中的指定字段
            ai_data[field_name] = content
            ai_data["timestamp"] = datetime.now().isoformat()
            
            # 保存到内存
            self.ai_optimized_data[cache_key] = ai_data
            
            # 保存到AI优化文件
            self._save_ai_cache( ai_data)
            
            print(f"✅ 保存{field_name}编辑: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存{field_name}缓存失败: {e}")

    def _save_instr_cache(self, content):
        """保存开场白编辑到instr字段（基于直播地址参数）"""
        self._save_content_cache(content, "instr")

    def _save_end_cache(self, content):
        """保存退场白编辑到end字段（基于直播地址参数）"""
        self._save_content_cache(content, "end")
    

    
    def _calculate_content_md5(self, original_data, optimized_content):
        """计算原始数据和优化内容的MD5用于变化检测"""
        try:
            # 获取prompt_template（如果存在）
            prompt_template = ""
            if hasattr(self, 'ai_optimizer') and self.ai_optimizer:
                self.ai_optimizer.init_prompt('prompty')
                prompt_template = self.ai_optimizer.prompt_template
            
            # 将optimized_content转换为字符串
            optimized_str = json.dumps(optimized_content, ensure_ascii=False, sort_keys=True)
            
            # 计算MD5
            combined_content = prompt_template + optimized_str
            md5_hash = hashlib.md5(combined_content.encode('utf-8')).hexdigest()
            
            return md5_hash
        except Exception as e:
            print(f"计算MD5失败: {e}")
            return None
    
    def _clean_json_content(self, content):
        """清理JSON内容，移除无效的控制字符"""
        import re
        
        # 移除常见的无效控制字符，但保留有效的空白字符
        # \x00-\x1F 是ASCII控制字符，但保留 \t(\x09), \n(\x0A), \r(\x0D)
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
        
        # 规范化换行符
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除过多的连续空行
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        return content.strip()
    

    
    def _save_ai_cache(self,ai_result):
        """保存AI优化缓存"""
        try:
            # 添加缓存键到结果中
            ai_result["cache_key"] = self._get_ai_cache_key()
            ai_result["cached_timestamp"] = datetime.now().isoformat()
            

            
            cache_file = self._get_ai_cache_file_path()
            
            # 确保目录存在
            cache_dir = os.path.dirname(cache_file)
            if not os.path.exists(cache_dir):
                os.makedirs(cache_dir)

            # 清理结果中的字符串内容
            cleaned_result = self._clean_cache_data(ai_result)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_result, f, indent=2, ensure_ascii=False)
            print(f"✅ AI缓存已保存: {os.path.basename(cache_file)}")
        except Exception as e:
            print(f"❌ 保存AI缓存失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _clean_cache_data(self, data):
        """递归清理缓存数据中的字符串内容"""
        if isinstance(data, dict):
            return {key: self._clean_cache_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._clean_cache_data(item) for item in data]
        elif isinstance(data, str):
            return self._clean_json_content(data)
        else:
            return data
    

    

    
    def _collect_jbs_intervals_from_original_data(self, original_data):
        """从原始数据中收集所有有效的Jbs区间信息 - 仅收集刹车操作(act_id=3)"""
        try:
            project_jbs_map = {}
            all_intervals = []
            
            if not isinstance(original_data, list):
                return project_jbs_map, all_intervals
            
            for item in original_data:
                if not isinstance(item, dict):
                    continue
                    
                # 获取项目信息
                info = item.get("info", {})
                project_name = info.get("name", "")
                
                # 获取Jbs列表
                jbs_list = item.get("Jbs", [])
                if not isinstance(jbs_list, list):
                    continue
                
                # 处理有效的Jbs区间 - 仅处理刹车操作
                valid_intervals = []
                for jbs in jbs_list:
                    if not isinstance(jbs, dict):
                        continue
                        
                    try:
                        # 只处理刹车操作 (act_id = "3")
                        act_id = jbs.get("act_id", "")
                        if act_id != "3":
                            continue
                        
                        min_time = float(jbs.get("min", "0"))
                        max_time = float(jbs.get("max", "0"))
                        txt = jbs.get("txt", "")
                        
                        # 只保留min>0且max>0的有效刹车区间
                        if min_time > 0 and max_time > 0 and max_time > min_time:
                            interval_info = {
                                "min": min_time,
                                "max": max_time,
                                "txt": txt,
                                "project": project_name,
                                "act_id": act_id
                            }
                            valid_intervals.append(interval_info)
                            all_intervals.append(interval_info)
                        # print(f"   📍 收集刹车区间: {project_name} - {txt} ({min_time:.3f}~{max_time:.3f}) [act_id={act_id}]")
                        
                    except (ValueError, TypeError) as e:
                        print(f"   ⚠️ 解析Jbs时间失败: {jbs} - {e}")
                        continue
                
                # 保存项目的刹车Jbs映射
                if project_name and valid_intervals:
                    project_jbs_map[project_name] = valid_intervals
            
            print(f"✅ 收集完成: {len(project_jbs_map)}个项目, {len(all_intervals)}个有效刹车区间")
            return project_jbs_map, all_intervals
            
        except Exception as e:
            print(f"❌ 收集Jbs区间失败: {e}")
            return {}, []
    
    def _is_timestamp_in_interval(self, timestamp, interval):
        """检查时间戳是否在指定区间内"""
        try:
            time_float = float(timestamp)
            return interval["min"] <= time_float <= interval["max"]
        except (ValueError, TypeError):
            return False
    
    def _adjust_timestamp_for_jbs_conflict(self, timestamp, all_intervals, project_name):
        """调整时间戳以避免与同项目刹车区间冲突 - 仅限刹车操作(act_id=3)"""
        try:
            original_time = float(timestamp)
            safety_margin = 3.0  # 安全间隔：语音开始前至少3秒
            
            # 查找同项目的刹车冲突区间
            conflicting_intervals = []
            
            for interval in all_intervals:
                # 只检查同项目的刹车区间 (act_id="3")
                if interval.get("project", "") != project_name:
                    continue
                
                if interval.get("act_id", "") != "3":
                    continue
                
                # 检查是否在区间内部，或者距离区间开始时间过近
                time_conflict = (
                    self._is_timestamp_in_interval(timestamp, interval) or  # 在区间内
                    (original_time >= interval["min"] - safety_margin and original_time < interval["min"])  # 距离开始过近
                )
                
                if time_conflict:
                    conflicting_intervals.append(interval)
            
            # 如果没有冲突，返回原始时间戳
            if not conflicting_intervals:
                return timestamp
            
            # 找到最大的max值，作为调整基准
            max_end_time = max(interval["max"] for interval in conflicting_intervals)
            adjusted_time = max_end_time + 1.0
            
            # 记录调整信息
            conflict_descriptions = [f"{interval['txt']}({interval['min']:.3f}~{interval['max']:.3f})" 
                                   for interval in conflicting_intervals]
            # print(f"   🔄 时间戳调整[同项目刹车]: {project_name} - {original_time:.3f} → {adjusted_time:.3f}")
            # print(f"      冲突刹车区间: {', '.join(conflict_descriptions)}")
            # print(f"      安全间隔: {safety_margin}秒")
            
            return f"{adjusted_time:.6f}"
            
        except (ValueError, TypeError) as e:
            print(f"   ⚠️ 时间戳调整失败: {timestamp} - {e}")
            return timestamp
    
    def _handle_multiple_timestamps_in_same_interval(self, temp_voice_data, all_intervals):
        """处理同一区间内多个时间戳的顺序调整"""
        try:
            # 按调整后的时间戳排序
            sorted_items = sorted(temp_voice_data.items(), key=lambda x: float(x[0]))
            
            # 检查是否有时间戳冲突（相同的调整后时间戳）
            adjusted_voice_data = {}
            timestamp_counts = {}
            
            for adjusted_timestamp, voice_info in sorted_items:
                try:
                    base_time = float(adjusted_timestamp)
                    
                    # 检查是否已经有相同时间戳
                    if adjusted_timestamp in timestamp_counts:
                        # 有冲突，需要进一步调整
                        timestamp_counts[adjusted_timestamp] += 1
                        new_adjusted_time = base_time + timestamp_counts[adjusted_timestamp] - 1
                        final_timestamp = f"{new_adjusted_time:.6f}"
                        
                        print(f"   🔄 处理时间戳冲突: {adjusted_timestamp} → {final_timestamp} ({voice_info['project']})")
                    else:
                        # 无冲突
                        timestamp_counts[adjusted_timestamp] = 1
                        final_timestamp = adjusted_timestamp
                    
                    adjusted_voice_data[final_timestamp] = voice_info
                    
                except (ValueError, TypeError) as e:
                    print(f"   ⚠️ 处理时间戳排序失败: {adjusted_timestamp} - {e}")
                    # 保持原始调整后的时间戳
                    adjusted_voice_data[adjusted_timestamp] = voice_info
            
            return adjusted_voice_data
            
        except Exception as e:
            print(f"❌ 处理多时间戳调整失败: {e}")
            return temp_voice_data
    
    def setup_gui(self):
        """设置主界面"""
        # 创建标签页容器
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 配置标签页样式 - 增大字体和标签尺寸，实现视觉居中效果
        style = ttk.Style()
        
        # 设置标签页样式
        style.configure('Custom.TNotebook.Tab', 
                       padding=[50, 15],  # 大幅增加水平内边距实现视觉居中
                       font=('Microsoft YaHei', 14, 'bold'),  # 增大字体到14号
                       width=20,  # 设置固定宽度让标签看起来更均匀
                       justify='center',  # 文字居中对齐
                       anchor='center')  # 锚点居中
        
        # 配置整个notebook的样式
        style.configure('Custom.TNotebook', 
                       tabposition='n')  # 确保标签在顶部
        
        self.notebook.configure(style='Custom.TNotebook')
        
        # 创建页面实例
        if IndexPage:
            self.index_page = IndexPage(self)
            self.index_page.setup_home_tab()
        
        if TxtPage:
            self.txt_page = TxtPage(self)
            self.txt_page.setup_content_tab()
        
        if SettingPage:
            self.setting_page = SettingPage(self)
            self.setting_page.setup_config_tab()
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
        
    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def load_cache(self):
        """加载缓存数据（基于缓存键）"""
        try:
            # 从AI优化缓存中加载原始数据
            self._load_all_ai_cache()
            
            # 从AI优化数据中提取原始数据到cached_data，同时加载推广文案数据
            cache_count = 0
            adv_count = 0
            for cache_key, ai_data in self.ai_optimized_data.items():
                original_data = ai_data.get('original_data')
                if original_data:
                    self.cached_data[cache_key] = original_data
                    cache_count += 1
                
                # 加载推广文案数据（优先从adv_org字段，兼容旧的adv_data字段）
                adv_data = ai_data.get('adv_org') or ai_data.get('adv_data')
                if adv_data:
                    self.adv_data[cache_key] = adv_data
                    adv_count += 1
            
            if cache_count > 0:
                print(f"✅ 从AI优化缓存加载原始数据成功，共 {cache_count} 项")
            if adv_count > 0:
                print(f"✅ 从AI优化缓存加载推广文案数据成功，共 {adv_count} 项")
        except Exception as e:
            print(f"❌ 缓存加载失败: {e}")
    
    def _load_all_ai_cache(self):
        """加载所有AI缓存（基于缓存键）"""
        try:
            ai_cache_count = 0
            if os.path.exists(self.ai_cache_dir):
                for filename in os.listdir(self.ai_cache_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(self.ai_cache_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                cache_data = json.load(f)
                                

                                
                                # 使用缓存键作为键值，而不是URL
                                cache_key = cache_data.get('cache_key')
                                if cache_key:
                                    self.ai_optimized_data[cache_key] = cache_data
                                    ai_cache_count += 1
                                else:
                                    print(f"⚠️ AI缓存文件缺少cache_key字段: {filename}")
                        except Exception as e:
                            print(f"❌ 加载AI缓存文件失败 {filename}: {e}")
            
            if ai_cache_count > 0:
                print(f"✅ 加载AI缓存成功，共 {ai_cache_count} 项")
        except Exception as e:
            print(f"❌ AI缓存加载失败: {e}")
    

    
    def on_tab_changed(self, event):
        """标签页切换事件处理"""
        try:
            selected_tab = self.notebook.index(self.notebook.select())
            tab_text = self.notebook.tab(selected_tab, "text")
            if "文案" in tab_text and self.txt_page:
                self.txt_page.display_cached_data()
        except Exception as e:
            print(f"❌ 标签页切换事件处理异常: {e}")
    
    def on_url_changed(self, *args):
        """URL变化事件处理 - 委托给TXT页面"""
        if self.txt_page:
            self.txt_page.on_url_changed(*args)
    
    def update_status(self, status, color="black"):
        """更新状态显示（仅控制台输出）"""
        # 根据颜色确定状态图标
        color_icons = {
            "green": "✅",
            "orange": "🔄", 
            "red": "❌",
            "gray": "ℹ️"
        }
        icon = color_icons.get(color, "📝")
        print(f"{icon} 状态: {status}")
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 清理音频控制器资源
            if hasattr(self, 'index_page') and self.index_page and hasattr(self.index_page, 'audio_controller'):
                self.index_page.audio_controller.cleanup()
                print("✅ 音频控制器资源已清理")
        except Exception as e:
            print(f"❌ 清理音频资源失败: {e}")
        
        self.root.quit()
        self.root.destroy()
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    app = IntegratedLiveStreamLauncher()
    app.run()

if __name__ == "__main__":
    main() 