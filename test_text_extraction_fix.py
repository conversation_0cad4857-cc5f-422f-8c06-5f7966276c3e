#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的文本提取功能
验证从主播稿文件中正确提取文本内容
"""
import os
import sys
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_voice_processor_text_extraction():
    """测试VoiceProcessor的文本提取功能"""
    print("🧪 测试VoiceProcessor文本提取功能")
    print("=" * 60)
    
    try:
        from src.page.index.voice import VoiceProcessor
        
        # 创建模拟的主控制器
        class MockMainController:
            def __init__(self):
                self.zhibo_cache_dir = "src/cache/zhibo"
                self.tts_plugin = None
                
            def _get_ai_cache_key(self):
                return "a3ee86ff033eb4c6556150453704f738"
        
        # 创建VoiceProcessor实例
        main_controller = MockMainController()
        voice_processor = VoiceProcessor(main_controller)
        
        print("✅ VoiceProcessor创建成功")
        
        # 测试文本提取功能
        cache_key = main_controller._get_ai_cache_key()
        zhubo_file_path = voice_processor._get_zhubo_file_path(cache_key)
        
        if os.path.exists(zhubo_file_path):
            print(f"📁 找到主播稿文件: {os.path.basename(zhubo_file_path)}")
            
            # 加载主播稿数据
            zhubo_data = voice_processor._load_zhubo_data(zhubo_file_path)
            print(f"📖 加载主播稿数据: {len(zhubo_data)} 个项目")
            
            # 提取文本内容
            all_texts = voice_processor._extract_texts_from_zhubo_data(zhubo_data)
            print(f"📝 提取文本内容: {len(all_texts)} 条")
            
            if len(all_texts) > 0:
                print("✅ 文本提取成功！")
                
                # 显示前5条提取的文本
                print("\n📋 提取的文本示例:")
                for i, (timestamp, text) in enumerate(list(all_texts.items())[:5]):
                    print(f"   {i+1}. {timestamp}: {text[:80]}...")
                
                # 测试MD5路径生成
                from src.utils.tts_hash_base import TTSHashBase
                hash_generator = TTSHashBase()
                
                print("\n🔗 测试MD5路径生成:")
                test_count = 0
                existing_count = 0
                
                for timestamp, text in list(all_texts.items())[:5]:
                    test_count += 1
                    generated_path = hash_generator._generate_text_path(text)
                    file_exists = os.path.exists(generated_path)
                    if file_exists:
                        existing_count += 1
                    
                    print(f"   {test_count}. {timestamp}")
                    print(f"      文本: {text[:50]}...")
                    print(f"      路径: {generated_path}")
                    print(f"      存在: {'✅' if file_exists else '❌'}")
                
                print(f"\n📊 MD5路径测试结果: {existing_count}/{test_count} 个文件存在")
                
                return True
            else:
                print("❌ 文本提取失败，返回空结果")
                return False
        else:
            print(f"❌ 未找到主播稿文件: {zhubo_file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zhubo1_text_extraction():
    """测试Zhubo1监听器的文本提取功能"""
    print("\n🧪 测试Zhubo1监听器文本提取功能")
    print("=" * 60)
    
    try:
        from src.anchors.zhubo1 import ZhuboListener
        
        # 创建监听器实例
        listener = ZhuboListener()
        
        print("✅ ZhuboListener创建成功")
        
        # 测试加载数据
        cache_key = "a3ee86ff033eb4c6556150453704f738"
        success = listener.load_latest_zhibo_data(cache_key)
        
        if success:
            print("✅ 数据加载成功")
            
            # 检查原始文本数据
            has_texts = hasattr(listener, 'original_texts') and listener.original_texts
            print(f"📝 原始文本数据: {'✅' if has_texts else '❌'}")
            
            if has_texts:
                print(f"📊 原始文本数量: {len(listener.original_texts)}")
                
                # 显示前5条文本
                print("\n📋 提取的文本示例:")
                for i, (timestamp, text) in enumerate(list(listener.original_texts.items())[:5]):
                    print(f"   {i+1}. {timestamp}: {text[:80]}...")
                
                # 测试音频查找功能
                print("\n🎵 测试音频查找功能:")
                test_timestamps = list(listener.original_texts.keys())[:3]
                
                for timestamp in test_timestamps:
                    try:
                        time_value = float(timestamp)
                        audio_info = listener.find_matching_audio(time_value)
                        
                        if audio_info:
                            print(f"   ✅ 找到音频: {timestamp}s")
                            print(f"      路径: {audio_info['path']}")
                            print(f"      存在: {'✅' if os.path.exists(audio_info['path']) else '❌'}")
                        else:
                            print(f"   ❌ 未找到音频: {timestamp}s")
                    except ValueError:
                        print(f"   ⚠️ 跳过非数字时间戳: {timestamp}")
                
                return True
            else:
                print("❌ 未找到原始文本数据")
                return False
        else:
            print("❌ 数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_structure_analysis():
    """分析数据结构"""
    print("\n🔍 数据结构分析")
    print("=" * 60)
    
    try:
        # 直接读取主播稿文件
        zhubo_file = "src/cache/zhibo/a3ee86ff033eb4c6556150453704f738.json"
        
        if not os.path.exists(zhubo_file):
            print(f"❌ 文件不存在: {zhubo_file}")
            return False
        
        with open(zhubo_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📁 文件: {os.path.basename(zhubo_file)}")
        print(f"📊 数据类型: {type(data)}")
        print(f"📊 项目数量: {len(data)}")
        
        # 分析前几个项目的结构
        print("\n📋 数据结构分析:")
        for i, item in enumerate(data[:3]):
            print(f"   项目 {i+1}:")
            print(f"      类型: {type(item)}")
            if isinstance(item, dict) and 'list' in item:
                item_list = item['list']
                print(f"      list类型: {type(item_list)}")
                print(f"      list长度: {len(item_list)}")
                
                # 分析list中的内容
                for j, (key, value) in enumerate(list(item_list.items())[:2]):
                    print(f"         {j+1}. 键: '{key}' (类型: {type(key)})")
                    print(f"            值类型: {type(value)}")
                    if isinstance(value, dict):
                        print(f"            值键: {list(value.keys())}")
                        if 'txt' in value:
                            txt_content = value['txt']
                            print(f"            txt长度: {len(txt_content)}")
                            print(f"            txt预览: {txt_content[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 文本提取修复验证测试")
    print("=" * 80)
    
    # 数据结构分析
    structure_test = test_data_structure_analysis()
    
    # 测试VoiceProcessor
    voice_test = test_voice_processor_text_extraction()
    
    # 测试Zhubo1监听器
    zhubo_test = test_zhubo1_text_extraction()
    
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"   数据结构分析: {'✅ 通过' if structure_test else '❌ 失败'}")
    print(f"   VoiceProcessor文本提取: {'✅ 通过' if voice_test else '❌ 失败'}")
    print(f"   Zhubo1监听器文本提取: {'✅ 通过' if zhubo_test else '❌ 失败'}")
    
    if structure_test and voice_test and zhubo_test:
        print("\n🎉 所有文本提取功能测试通过！")
        print("✅ 文本提取问题已修复")
        print("🔧 MD5即时编码功能现在可以正常工作")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
