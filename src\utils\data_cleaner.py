 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗模块
用于对原始数据进行清洗处理，去除不需要的字段
可被多个AI插件复用（DeepSeek、豆包等）
"""

import json
import copy
from typing import Dict, Any, List, Union
from datetime import datetime


class DataCleaner:
    """数据清洗器 - 专门处理驾考数据的清洗任务"""
    
    def __init__(self):
        """初始化数据清洗器"""
        self.clean_operations = {
            "remove_jbs": self._remove_jbs_fields,
            "remove_empty_fields": self._remove_empty_fields,
            "normalize_timestamps": self._normalize_timestamps
        }
    
    def clean_for_ai_processing(self, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        为AI处理准备清洗后的数据
        主要用于去除Jbs和info字段，减少AI处理的数据量和复杂度
        
        Args:
            original_data: 包含原始数据的字典，格式: {"data": [...], "url": "...", ...}
            
        Returns:
            清洗后的数据字典，保持相同结构但移除了Jbs和info字段
        """
        try:
            # 深拷贝原始数据，避免修改原数据
            cleaned_data = copy.deepcopy(original_data)
            
            # 获取数据数组
            data_array = cleaned_data.get("data", [])
            if not isinstance(data_array, list):
                print("⚠️ 数据格式不正确，期望数组格式")
                return cleaned_data
            
            # 对每个项目清除Jbs和info字段
            jbs_cleaned_count = 0
            info_cleaned_count = 0
            for item in data_array:
                if isinstance(item, dict):
                    if "Jbs" in item:
                        del item["Jbs"]
                        jbs_cleaned_count += 1
                    if "info" in item:
                        del item["info"]
                        info_cleaned_count += 1
                    if "jbs_txt" in item:
                        del item["jbs_txt"]
            # 添加清洗元数据
            cleaned_data["cleaning_info"] = {
                "timestamp": datetime.now().isoformat(),
                "operations": ["remove_jbs", "remove_info"],
                "jbs_cleaned_count": jbs_cleaned_count,
                "info_cleaned_count": info_cleaned_count,
                "total_items": len(data_array)
            }
            
            # 输出清理日志
            if jbs_cleaned_count > 0 or info_cleaned_count > 0:
                log_parts = []
                if jbs_cleaned_count > 0:
                    log_parts.append(f"{jbs_cleaned_count} 个Jbs字段")
                if info_cleaned_count > 0:
                    log_parts.append(f"{info_cleaned_count} 个info字段")
                print(f"✅ 数据清洗: 清理了 {', '.join(log_parts)}")
            
            return cleaned_data
            
        except Exception as e:
            print(f"❌ 数据清洗失败: {e}")
            # 出错时返回原始数据
            return original_data
    
    def _remove_jbs_fields(self, data_array: List[Dict]) -> List[Dict]:
        """移除所有Jbs字段"""
        cleaned_array = []
        for item in data_array:
            if isinstance(item, dict):
                cleaned_item = {k: v for k, v in item.items() if k != "Jbs"}
                cleaned_array.append(cleaned_item)
            else:
                cleaned_array.append(item)
        return cleaned_array
    
    def _remove_empty_fields(self, data_array: List[Dict]) -> List[Dict]:
        """移除空字段"""
        cleaned_array = []
        for item in data_array:
            if isinstance(item, dict):
                cleaned_item = {k: v for k, v in item.items() 
                              if v is not None and v != "" and v != []}
                cleaned_array.append(cleaned_item)
            else:
                cleaned_array.append(item)
        return cleaned_array
    
    def _normalize_timestamps(self, data_array: List[Dict]) -> List[Dict]:
        """标准化时间戳格式"""
        for item in data_array:
            if isinstance(item, dict) and "list" in item:
                item_list = item["list"]
                if isinstance(item_list, dict):
                    # 确保时间戳为字符串格式
                    normalized_list = {}
                    for timestamp, content in item_list.items():
                        normalized_list[str(timestamp)] = content
                    item["list"] = normalized_list
        return data_array
    

    
    def validate_cleaned_data(self, cleaned_data: Dict[str, Any]) -> bool:
        """
        验证清洗后的数据完整性
        
        Args:
            cleaned_data: 清洗后的数据
            
        Returns:
            是否通过验证
        """
        try:
            # 检查基本结构
            if not isinstance(cleaned_data, dict):
                return False
            
            if "data" not in cleaned_data:
                return False
            
            data_array = cleaned_data["data"]
            if not isinstance(data_array, list):
                return False
            
            # 检查是否还存在Jbs或info字段
            for item in data_array:
                if isinstance(item, dict):
                    if "Jbs" in item:
                        print("⚠️ 验证失败：仍存在Jbs字段")
                        return False
                    if "info" in item:
                        print("⚠️ 验证失败：仍存在info字段")
                        return False
            
            # 检查必需字段（移除了info字段后，只检查list字段）
            for item in data_array:
                if isinstance(item, dict):
                    if "list" not in item:
                        print("⚠️ 验证失败：缺少必需字段list")
                        return False
            
            # 验证通过时不输出日志，保持简洁
            return True
            
        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False


# 工厂函数，方便其他模块使用
def create_data_cleaner() -> DataCleaner:
    """创建数据清洗器实例"""
    return DataCleaner()


def clean_data_for_ai(original_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：为AI处理清洗数据
    
    Args:
        original_data: 原始数据
        
    Returns:
        清洗后的数据
    """
    cleaner = create_data_cleaner()
    return cleaner.clean_for_ai_processing(original_data)


if __name__ == "__main__":
    # 测试代码
    test_data = {
        "url": "http://example.com",
        "data": [
            {
                "info": {"name": "测试项目1"},
                "list": {"10.5": "测试文案1"},
                "Jbs": [{"act_id": "1", "min": "10", "max": "20", "txt": "测试动作"}]
            },
            {
                "info": {"name": "测试项目2"},
                "list": {"20.5": "测试文案2"},
                "Jbs": [{"act_id": "2", "min": "25", "max": "30", "txt": "测试动作2"}]
            }
        ]
    }
    
    cleaner = DataCleaner()
    cleaned = cleaner.clean_for_ai_processing(test_data)
    summary = cleaner.get_cleaning_summary(test_data, cleaned)
    is_valid = cleaner.validate_cleaned_data(cleaned)
    
    print("原始数据:", json.dumps(test_data, indent=2, ensure_ascii=False))
    print("\n清洗后数据:", json.dumps(cleaned, indent=2, ensure_ascii=False))
    print("\n清洗摘要:", json.dumps(summary, indent=2, ensure_ascii=False))
    print(f"\n验证结果: {is_valid}")